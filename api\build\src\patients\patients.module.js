"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const patients_controller_1 = require("./patients.controller");
const patients_service_1 = require("./patients.service");
const patient_entity_1 = require("./entities/patient.entity");
const patient_owner_entity_1 = require("./entities/patient-owner.entity");
const role_module_1 = require("../roles/role.module");
const clinic_entity_1 = require("../clinics/entities/clinic.entity");
const global_reminder_rule_entity_1 = require("../patient-global-reminders/entities/global-reminder-rule.entity");
const patient_reminder_entity_1 = require("../patient-reminders/entities/patient-reminder.entity");
const global_reminders_service_1 = require("../patient-global-reminders/global-reminders.service");
const global_reminders_module_1 = require("../patient-global-reminders/global-reminders.module");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const owners_service_1 = require("../owners/owners.service");
const global_owner_entity_1 = require("../owners/entities/global-owner.entity");
const owner_brand_entity_1 = require("../owners/entities/owner-brand.entity");
const brand_entity_1 = require("../brands/entities/brand.entity");
const whatsapp_module_1 = require("../utils/whatsapp-integration/whatsapp.module");
const ses_module_1 = require("../utils/aws/ses/ses.module");
const pet_transfer_history_entity_1 = require("../owners/entities/pet-transfer-history.entity");
const payment_details_entity_1 = require("../payment-details/entities/payment-details.entity");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
let PatientsModule = class PatientsModule {
};
exports.PatientsModule = PatientsModule;
exports.PatientsModule = PatientsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                patient_entity_1.Patient,
                patient_owner_entity_1.PatientOwner,
                clinic_entity_1.ClinicEntity,
                global_reminder_rule_entity_1.GlobalReminderRule,
                patient_reminder_entity_1.PatientReminder,
                global_owner_entity_1.GlobalOwner,
                owner_brand_entity_1.OwnerBrand,
                brand_entity_1.Brand,
                pet_transfer_history_entity_1.PetTransferHistory,
                payment_details_entity_1.PaymentDetailsEntity,
                invoice_entity_1.InvoiceEntity
            ]),
            role_module_1.RoleModule,
            global_reminders_module_1.GlobalReminderModule,
            whatsapp_module_1.WhatsappModule,
            ses_module_1.SESModule
        ],
        controllers: [patients_controller_1.PatientsController],
        providers: [
            patients_service_1.PatientsService,
            winston_logger_service_1.WinstonLogger,
            global_reminders_service_1.GlobalReminderService,
            owners_service_1.OwnersService
        ],
        exports: [patients_service_1.PatientsService]
    })
], PatientsModule);
//# sourceMappingURL=patients.module.js.map