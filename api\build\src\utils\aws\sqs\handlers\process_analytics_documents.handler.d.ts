import { Message } from '@aws-sdk/client-sqs';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../interfaces/queue-handler.interface';
import { <PERSON><PERSON>og<PERSON> } from '../../../logger/winston-logger.service';
import { AnalyticsDocumentService } from '../../../../analytics-sharing/services/analytics-document.service';
export declare class ProcessAnalyticsDocumentsHandler implements QueueHandler {
    private readonly analyticsDocumentService;
    private readonly logger;
    constructor(analyticsDocumentService: AnalyticsDocumentService, logger: <PERSON><PERSON>ogger);
    handle(message: Message): Promise<void>;
}
