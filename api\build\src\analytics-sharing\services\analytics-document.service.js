"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsDocumentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const winston_logger_service_1 = require("../../utils/logger/winston-logger.service");
const users_service_1 = require("../../users/users.service");
const analytics_document_request_entity_1 = require("../entities/analytics-document-request.entity");
const invoice_entity_1 = require("../../invoice/entities/invoice.entity");
const payment_details_entity_1 = require("../../payment-details/entities/payment-details.entity");
const enum_credit_types_1 = require("../../payment-details/enums/enum-credit-types");
const enum_invoice_types_1 = require("../../invoice/enums/enum-invoice-types");
const patient_entity_1 = require("../../patients/entities/patient.entity");
const owner_brand_entity_1 = require("../../owners/entities/owner-brand.entity");
const clinic_entity_1 = require("../../clinics/entities/clinic.entity");
const s3_service_1 = require("../../utils/aws/s3/s3.service");
const XLSX = require("xlsx");
const generatePdf_1 = require("../../utils/generatePdf");
const merge_pdf_image_into_1 = require("../../utils/merge-pdf-image-into");
const generateInvoice_1 = require("../../utils/pdfs/new/generateInvoice");
const generateCreditNote_1 = require("../../utils/pdfs/new/generateCreditNote");
const generatePaymentReceipt_1 = require("../../utils/pdfs/new/generatePaymentReceipt");
const moment = require("moment");
let AnalyticsDocumentService = class AnalyticsDocumentService {
    constructor(logger, analyticsDocumentRequestRepository, invoiceRepository, paymentDetailsRepository, patientRepository, ownerBrandRepository, clinicRepository, s3Service, usersService) {
        this.logger = logger;
        this.analyticsDocumentRequestRepository = analyticsDocumentRequestRepository;
        this.invoiceRepository = invoiceRepository;
        this.paymentDetailsRepository = paymentDetailsRepository;
        this.patientRepository = patientRepository;
        this.ownerBrandRepository = ownerBrandRepository;
        this.clinicRepository = clinicRepository;
        this.s3Service = s3Service;
        this.usersService = usersService;
    }
    /**
     * Share analytics documents (PDF + Excel) via email
     */
    async shareAnalyticsDocuments(request) {
        try {
            // Resolve recipient email for CLIENT type
            let recipientEmail = request.recipientEmail;
            if (request.recipientType === analytics_document_request_entity_1.AnalyticsRecipientType.CLIENT) {
                // For CLIENT type, automatically use the current user's email
                const user = await this.usersService.findOne(request.userId);
                recipientEmail = user.email;
            }
            // Log email details for debugging
            this.logger.log('📧 Analytics Document Email Details:', {
                recipientType: request.recipientType,
                recipientEmail: recipientEmail,
                documentType: request.documentType,
                dateRange: `${request.startDate.toISOString().split('T')[0]} to ${request.endDate.toISOString().split('T')[0]}`,
                requestId: request.requestId
            });
            // Create analytics document request record
            const analyticsRequest = this.analyticsDocumentRequestRepository.create({
                clinicId: request.clinicId,
                brandId: request.brandId,
                userId: request.userId,
                documentType: request.documentType,
                recipientType: request.recipientType,
                recipientEmail: recipientEmail,
                recipientPhone: request.recipientPhone,
                startDate: request.startDate,
                endDate: request.endDate,
                status: analytics_document_request_entity_1.AnalyticsDocumentStatus.PENDING
            });
            // Set the ID manually after creation
            analyticsRequest.id = request.requestId;
            await this.analyticsDocumentRequestRepository.save(analyticsRequest);
            // Process documents immediately (Phase 1 - synchronous processing)
            // In Phase 2, this will be moved to background processing
            await this.processAnalyticsDocuments(request.requestId);
            this.logger.log('Analytics document request created and processed', {
                requestId: request.requestId,
                documentType: request.documentType,
                recipientType: request.recipientType
            });
            return request.requestId;
        }
        catch (error) {
            this.logger.error('Failed to create analytics document request', {
                requestId: request.requestId,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Get analytics document request status
     */
    async getAnalyticsDocumentStatus(requestId) {
        const request = await this.analyticsDocumentRequestRepository.findOne({
            where: { id: requestId }
        });
        if (!request) {
            throw new common_1.NotFoundException(`Analytics document request with ID ${requestId} not found`);
        }
        return {
            id: request.id,
            status: request.status,
            documentType: request.documentType,
            recipientType: request.recipientType,
            recipientEmail: request.recipientEmail,
            createdAt: request.createdAt,
            updatedAt: request.updatedAt,
            expiresAt: request.expiresAt,
            errorMessage: request.errorMessage,
            processedAt: request.processedAt,
            documentCount: request.documentCount,
            totalSize: request.totalSize
        };
    }
    /**
     * Process analytics documents in background (called by SQS handler)
     */
    async processAnalyticsDocuments(requestId) {
        try {
            // Update status to processing
            await this.analyticsDocumentRequestRepository.update({ id: requestId }, {
                status: analytics_document_request_entity_1.AnalyticsDocumentStatus.PROCESSING,
                updatedAt: new Date()
            });
            this.logger.log('🚀 Starting analytics document processing', {
                requestId
            });
            console.log(`\n=== 🚀 ANALYTICS PROCESSING STARTED ===`);
            console.log(`📋 Request ID: ${requestId}`);
            console.log(`⏰ Started at: ${new Date().toISOString()}`);
            console.log(`=== PROCESSING IN PROGRESS ===\n`);
            // Get the request details
            const request = await this.analyticsDocumentRequestRepository.findOne({
                where: { id: requestId }
            });
            if (!request) {
                throw new Error(`Analytics document request with ID ${requestId} not found`);
            }
            // Process documents by type (placeholder - will be implemented in Phase 2)
            const result = await this.processDocumentsByType(request);
            // Send email with documents (placeholder - will be implemented in Phase 2)
            await this.sendAnalyticsEmail(request, result);
            // Update status to completed
            await this.analyticsDocumentRequestRepository.update({ id: requestId }, {
                status: analytics_document_request_entity_1.AnalyticsDocumentStatus.COMPLETED,
                processedAt: new Date(),
                documentCount: result.documentCount,
                totalSize: result.totalSize,
                pdfFileKey: result.pdfFileKey,
                excelFileKey: result.excelFileKey,
                updatedAt: new Date()
            });
            this.logger.log('✅ Analytics document processing completed', {
                requestId,
                documentCount: result.documentCount,
                totalSize: result.totalSize
            });
            console.log(`\n=== ✅ ANALYTICS PROCESSING COMPLETED ===`);
            console.log(`📋 Request ID: ${requestId}`);
            console.log(`📄 Documents processed: ${result.documentCount}`);
            console.log(`📊 Total size: ${Math.round(result.totalSize / 1024)} KB`);
            console.log(`⏰ Completed at: ${new Date().toISOString()}`);
            console.log(`=== PROCESSING FINISHED ===\n`);
        }
        catch (error) {
            this.logger.error('Analytics document processing failed', {
                requestId,
                error: error instanceof Error ? error.message : String(error)
            });
            // Update status to failed
            await this.analyticsDocumentRequestRepository.update({ id: requestId }, {
                status: analytics_document_request_entity_1.AnalyticsDocumentStatus.FAILED,
                errorMessage: error instanceof Error ? error.message : String(error),
                updatedAt: new Date()
            });
            // Don't re-throw to avoid infinite SQS retries
        }
    }
    /**
     * Process documents by type - Generate both PDF and Excel files
     */
    async processDocumentsByType(request) {
        this.logger.log('Processing documents for analytics request', {
            requestId: request.id,
            documentType: request.documentType,
            startDate: request.startDate,
            endDate: request.endDate
        });
        // Ensure dates are proper Date objects
        const startDate = new Date(request.startDate);
        const endDate = new Date(request.endDate);
        // Validate period (max 1 month)
        const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        if (periodDays > 31) {
            throw new Error('Period cannot exceed 1 month (31 days)');
        }
        // Create request object with proper Date objects
        const requestWithDates = {
            ...request,
            startDate,
            endDate
        };
        // Fetch data based on document type
        let documentData = [];
        let excelData = {};
        switch (request.documentType) {
            case analytics_document_request_entity_1.AnalyticsDocumentType.INVOICE:
                documentData = await this.fetchInvoiceData(requestWithDates);
                excelData.invoices = await this.convertInvoicesToExcelFormat(documentData);
                break;
            case analytics_document_request_entity_1.AnalyticsDocumentType.RECEIPT:
                documentData = await this.fetchReceiptData(requestWithDates);
                excelData.receipts = this.convertReceiptsToExcelFormat(documentData);
                break;
            case analytics_document_request_entity_1.AnalyticsDocumentType.CREDIT_NOTE:
                documentData = await this.fetchCreditNoteData(requestWithDates);
                excelData.creditNotes = this.convertCreditNotesToExcelFormat(documentData);
                break;
            default:
                throw new Error(`Unsupported document type: ${request.documentType}`);
        }
        // Enforce document limit (max 5000)
        if (documentData.length > 5000) {
            throw new Error(`Too many documents found (${documentData.length}). Maximum allowed is 5000.`);
        }
        // Generate Excel buffer
        const excelBuffer = this.generateExcelReport(excelData, request.documentType);
        // Log document data before PDF generation
        this.logger.log('📄 Document data summary before PDF generation', {
            requestId: request.id,
            documentType: request.documentType,
            documentCount: documentData.length,
            sampleDocument: documentData.length > 0 ? {
                id: documentData[0].id,
                createdAt: documentData[0].createdAt,
                amount: documentData[0].invoiceAmount || documentData[0].amount || 'N/A'
            } : null
        });
        // Generate PDF buffer with actual document stitching
        const pdfBuffer = await this.generatePdfReport(documentData, request);
        const totalSize = excelBuffer.length + pdfBuffer.length;
        // Upload files to S3
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const pdfFileKey = `analytics-documents/${request.clinicId}/${request.documentType.toLowerCase()}-${timestamp}.pdf`;
        const excelFileKey = `analytics-documents/${request.clinicId}/${request.documentType.toLowerCase()}-${timestamp}.xlsx`;
        let pdfUploadResult;
        let excelUploadResult;
        try {
            // Upload PDF to S3
            pdfUploadResult = await this.s3Service.uploadPdfToS3(pdfBuffer, pdfFileKey);
            // Upload Excel to S3
            excelUploadResult = await this.uploadExcelToS3(excelBuffer, excelFileKey);
            this.logger.log('Files uploaded to S3', {
                requestId: request.id,
                pdfFileKey,
                excelFileKey
            });
        }
        catch (error) {
            this.logger.error('Failed to upload files to S3', {
                requestId: request.id,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
        this.logger.log('Document processing completed', {
            requestId: request.id,
            documentCount: documentData.length,
            totalSize,
            excelSize: excelBuffer.length,
            pdfSize: pdfBuffer.length
        });
        return {
            pdfBuffer,
            excelBuffer,
            documentCount: documentData.length,
            totalSize,
            pdfFileKey,
            excelFileKey
        };
    }
    /**
     * Fetch invoice data for the specified period
     */
    async fetchInvoiceData(request) {
        this.logger.log('DEBUG: Starting invoice fetch with parameters', {
            requestId: request.id,
            clinicId: request.clinicId,
            brandId: request.brandId,
            startDate: request.startDate,
            endDate: request.endDate
        });
        // First, let's check ALL invoices for this clinic (no filters)
        const allInvoicesForClinic = await this.invoiceRepository
            .createQueryBuilder('invoice')
            .where('invoice.clinicId = :clinicId', { clinicId: request.clinicId })
            .getMany();
        this.logger.log('DEBUG: All invoices for clinic', {
            requestId: request.id,
            clinicId: request.clinicId,
            totalInvoicesForClinic: allInvoicesForClinic.length
        });
        // Now check invoices for clinic + brand
        const invoicesForBrand = await this.invoiceRepository
            .createQueryBuilder('invoice')
            .where('invoice.clinicId = :clinicId', { clinicId: request.clinicId })
            .andWhere('invoice.brandId = :brandId', { brandId: request.brandId })
            .getMany();
        this.logger.log('DEBUG: Invoices for clinic + brand', {
            requestId: request.id,
            clinicId: request.clinicId,
            brandId: request.brandId,
            invoicesForBrand: invoicesForBrand.length
        });
        // Now add date filter
        const invoicesInDateRange = await this.invoiceRepository
            .createQueryBuilder('invoice')
            .where('invoice.clinicId = :clinicId', { clinicId: request.clinicId })
            .andWhere('invoice.brandId = :brandId', { brandId: request.brandId })
            .andWhere('invoice.createdAt >= :startDate', { startDate: request.startDate })
            .andWhere('invoice.createdAt <= :endDate', { endDate: request.endDate })
            .getMany();
        this.logger.log('DEBUG: Invoices in date range', {
            requestId: request.id,
            startDate: request.startDate,
            endDate: request.endDate,
            invoicesInDateRange: invoicesInDateRange.length
        });
        // Finally, add invoice type filter
        const invoices = await this.invoiceRepository
            .createQueryBuilder('invoice')
            .where('invoice.clinicId = :clinicId', { clinicId: request.clinicId })
            .andWhere('invoice.brandId = :brandId', { brandId: request.brandId })
            .andWhere('invoice.createdAt >= :startDate', { startDate: request.startDate })
            .andWhere('invoice.createdAt <= :endDate', { endDate: request.endDate })
            .andWhere('invoice.invoiceType = :invoiceType', { invoiceType: enum_invoice_types_1.EnumInvoiceType.Invoice })
            .orderBy('invoice.createdAt', 'DESC')
            .getMany();
        this.logger.log('Fetched invoice data', {
            requestId: request.id,
            count: invoices.length
        });
        return invoices;
    }
    /**
     * Fetch receipt data for the specified period
     */
    async fetchReceiptData(request) {
        const receipts = await this.paymentDetailsRepository
            .createQueryBuilder('payment')
            .leftJoinAndSelect('payment.patient', 'patient')
            .leftJoinAndSelect('payment.ownerBrand', 'ownerBrand')
            .where('payment.clinicId = :clinicId', { clinicId: request.clinicId })
            .andWhere('payment.brandId = :brandId', { brandId: request.brandId })
            .andWhere('payment.createdAt >= :startDate', { startDate: request.startDate })
            .andWhere('payment.createdAt <= :endDate', { endDate: request.endDate })
            .andWhere('payment.type IN (:...types)', {
            types: [enum_credit_types_1.EnumAmountType.Collect, enum_credit_types_1.EnumAmountType.Return]
        })
            .orderBy('payment.createdAt', 'DESC')
            .getMany();
        this.logger.log('Fetched receipt data', {
            requestId: request.id,
            count: receipts.length
        });
        return receipts;
    }
    /**
     * Fetch credit note data for the specified period
     */
    async fetchCreditNoteData(request) {
        // First, let's check what credit note data exists
        const allCreditNotes = await this.paymentDetailsRepository
            .createQueryBuilder('payment')
            .where('payment.clinicId = :clinicId', { clinicId: request.clinicId })
            .andWhere('payment.brandId = :brandId', { brandId: request.brandId })
            .andWhere('payment.type = :type', { type: enum_credit_types_1.EnumAmountType.CreditNote })
            .getMany();
        this.logger.log('All credit notes for clinic', {
            requestId: request.id,
            clinicId: request.clinicId,
            brandId: request.brandId,
            totalCreditNotes: allCreditNotes.length,
            dateRange: `${request.startDate} to ${request.endDate}`
        });
        // Now get credit notes for the specific date range
        const creditNotes = await this.paymentDetailsRepository
            .createQueryBuilder('payment')
            .where('payment.clinicId = :clinicId', { clinicId: request.clinicId })
            .andWhere('payment.brandId = :brandId', { brandId: request.brandId })
            .andWhere('payment.createdAt >= :startDate', { startDate: request.startDate })
            .andWhere('payment.createdAt <= :endDate', { endDate: request.endDate })
            .andWhere('payment.type = :type', { type: enum_credit_types_1.EnumAmountType.CreditNote })
            .orderBy('payment.createdAt', 'DESC')
            .getMany();
        this.logger.log('Fetched credit note data for date range', {
            requestId: request.id,
            count: creditNotes.length,
            startDate: request.startDate,
            endDate: request.endDate
        });
        // If we found credit notes, let's get the related data using direct repository queries
        const enrichedCreditNotes = [];
        for (const creditNote of creditNotes) {
            const patient = creditNote.patientId ? await this.patientRepository.findOne({
                where: { id: creditNote.patientId }
            }) : null;
            const owner = await this.ownerBrandRepository.findOne({
                where: { id: creditNote.ownerId }
            });
            enrichedCreditNotes.push({
                ...creditNote,
                patient,
                ownerBrand: owner
            });
        }
        return enrichedCreditNotes;
    }
    /**
     * Convert invoice data to Excel format
     */
    async convertInvoicesToExcelFormat(invoices) {
        const excelRows = [];
        for (const invoice of invoices) {
            // Fetch patient data using patientId
            const patient = await this.patientRepository.findOne({
                where: { id: invoice.patientId }
            });
            // Fetch owner data using ownerId
            const owner = await this.ownerBrandRepository.findOne({
                where: { id: invoice.ownerId }
            });
            const ownerName = owner
                ? `${owner.firstName || ''} ${owner.lastName || ''}`.trim()
                : 'N/A';
            const petName = (patient === null || patient === void 0 ? void 0 : patient.patientName) || 'N/A';
            excelRows.push({
                date: invoice.createdAt.toLocaleDateString('en-GB'),
                client: ownerName,
                pet: petName,
                invoiceNumber: invoice.referenceAlphaId || `#${invoice.referenceId}`,
                invoiceStatus: invoice.status || 'Unknown',
                invoiceAmount: Number(invoice.invoiceAmount) || 0,
                invoiceBalance: Number(invoice.balanceDue) || 0
            });
        }
        return excelRows;
    }
    /**
     * Upload Excel file to S3 with proper Promise handling
     */
    async uploadExcelToS3(excelBuffer, fileKey) {
        return new Promise((resolve, reject) => {
            const params = {
                Bucket: this.s3Service['bucketName'], // Access private property
                Key: fileKey,
                Body: excelBuffer,
                ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            };
            this.s3Service['s3Client'].putObject(params, (err, data) => {
                if (err) {
                    this.logger.error('S3 Excel upload error', {
                        fileKey,
                        error: err.message || err
                    });
                    reject(err);
                }
                else {
                    this.logger.log('Excel file uploaded to S3 successfully', {
                        fileKey
                    });
                    resolve(data);
                }
            });
        });
    }
    /**
     * Convert receipt data to Excel format
     */
    convertReceiptsToExcelFormat(receipts) {
        return receipts.map(receipt => {
            const ownerName = receipt.ownerBrand
                ? `${receipt.ownerBrand.firstName || ''} ${receipt.ownerBrand.lastName || ''}`.trim()
                : 'N/A';
            const transactionType = receipt.type === enum_credit_types_1.EnumAmountType.Collect ? 'Collected' : 'Returned';
            return {
                date: receipt.createdAt.toLocaleDateString('en-GB'),
                client: ownerName,
                receiptNumber: receipt.referenceAlphaId || `#${receipt.referenceId}`,
                amount: Number(receipt.amount) || 0,
                transaction: transactionType,
                paymentMode: receipt.paymentType || 'Cash'
            };
        });
    }
    /**
     * Convert credit note data to Excel format
     */
    convertCreditNotesToExcelFormat(creditNotes) {
        return creditNotes.map(creditNote => {
            var _a, _b;
            const ownerName = creditNote.ownerBrand
                ? `${creditNote.ownerBrand.firstName || ''} ${creditNote.ownerBrand.lastName || ''}`.trim()
                : 'N/A';
            const referenceInvoice = ((_a = creditNote.invoice) === null || _a === void 0 ? void 0 : _a.referenceAlphaId) ||
                (((_b = creditNote.invoice) === null || _b === void 0 ? void 0 : _b.referenceId) ? `#${creditNote.invoice.referenceId}` : 'N/A');
            return {
                date: creditNote.createdAt.toLocaleDateString('en-GB'),
                client: ownerName,
                creditNoteNumber: creditNote.referenceAlphaId || `#${creditNote.referenceId}`,
                referenceInvoice,
                amountReturned: Number(creditNote.amount) || 0
            };
        });
    }
    /**
     * Generate Excel report from data
     */
    generateExcelReport(data, documentType) {
        const workbook = XLSX.utils.book_new();
        switch (documentType) {
            case analytics_document_request_entity_1.AnalyticsDocumentType.INVOICE:
                if (data.invoices && data.invoices.length > 0) {
                    const worksheet = XLSX.utils.json_to_sheet(data.invoices);
                    // Set column widths for better formatting
                    worksheet['!cols'] = [
                        { width: 12 }, // Date
                        { width: 20 }, // Client
                        { width: 15 }, // Pet
                        { width: 15 }, // Invoice Number
                        { width: 15 }, // Invoice Status
                        { width: 15 }, // Invoice Amount
                        { width: 15 } // Invoice Balance
                    ];
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Invoices');
                }
                else {
                    // Create empty sheet with headers
                    const emptyData = [{
                            date: '',
                            client: '',
                            pet: '',
                            invoiceNumber: '',
                            invoiceStatus: '',
                            invoiceAmount: '',
                            invoiceBalance: ''
                        }];
                    const worksheet = XLSX.utils.json_to_sheet(emptyData);
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Invoices');
                }
                break;
            case analytics_document_request_entity_1.AnalyticsDocumentType.RECEIPT:
                if (data.receipts && data.receipts.length > 0) {
                    const worksheet = XLSX.utils.json_to_sheet(data.receipts);
                    worksheet['!cols'] = [
                        { width: 12 }, // Date
                        { width: 20 }, // Client
                        { width: 15 }, // Receipt Number
                        { width: 15 }, // Amount
                        { width: 15 }, // Transaction
                        { width: 15 } // Payment Mode
                    ];
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Receipts');
                }
                else {
                    const emptyData = [{
                            date: '',
                            client: '',
                            receiptNumber: '',
                            amount: '',
                            transaction: '',
                            paymentMode: ''
                        }];
                    const worksheet = XLSX.utils.json_to_sheet(emptyData);
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Receipts');
                }
                break;
            case analytics_document_request_entity_1.AnalyticsDocumentType.CREDIT_NOTE:
                if (data.creditNotes && data.creditNotes.length > 0) {
                    const worksheet = XLSX.utils.json_to_sheet(data.creditNotes);
                    worksheet['!cols'] = [
                        { width: 12 }, // Date
                        { width: 20 }, // Client
                        { width: 18 }, // Credit Note Number
                        { width: 18 }, // Reference Invoice
                        { width: 15 } // Amount Returned
                    ];
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Credit Notes');
                }
                else {
                    const emptyData = [{
                            date: '',
                            client: '',
                            creditNoteNumber: '',
                            referenceInvoice: '',
                            amountReturned: ''
                        }];
                    const worksheet = XLSX.utils.json_to_sheet(emptyData);
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Credit Notes');
                }
                break;
        }
        // Convert workbook to buffer
        const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
        this.logger.log('Excel report generated', {
            documentType,
            bufferSize: excelBuffer.length
        });
        return excelBuffer;
    }
    /**
     * Generate PDF report by stitching individual document PDFs
     */
    async generatePdfReport(documentData, request) {
        console.log('\n=== 📄 PDF GENERATION DEBUG ===');
        console.log(`📋 Request ID: ${request.id}`);
        console.log(`📄 Document Type: ${request.documentType}`);
        console.log(`📊 Document Count: ${documentData.length}`);
        console.log(`📅 Date Range: ${new Date(request.startDate).toDateString()} to ${new Date(request.endDate).toDateString()}`);
        if (documentData.length === 0) {
            console.log('⚠️  No documents found - generating "No Data" PDF');
            // Return a simple "No documents found" PDF
            const noDataHtml = `
				<!DOCTYPE html>
				<html>
				<head>
					<style>
						body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
						h1 { color: #666; }
					</style>
				</head>
				<body>
					<h1>No ${request.documentType.toLowerCase()}s found</h1>
					<p>No documents were found for the specified date range.</p>
					<p>Period: ${new Date(request.startDate).toDateString()} to ${new Date(request.endDate).toDateString()}</p>
				</body>
				</html>
			`;
            const buffer = await (0, generatePdf_1.generatePDFBuffer)(noDataHtml);
            console.log(`✅ "No Data" PDF generated successfully (${buffer.length} bytes)`);
            console.log('=== END PDF GENERATION ===\n');
            return buffer;
        }
        console.log(`🔄 Processing ${documentData.length} documents for PDF generation...`);
        const pdfBuffers = [];
        // Generate individual PDFs for each document
        for (let i = 0; i < documentData.length; i++) {
            const document = documentData[i];
            console.log(`📄 Processing document ${i + 1}/${documentData.length} (ID: ${document.id})`);
            try {
                let pdfBuffer;
                switch (request.documentType) {
                    case analytics_document_request_entity_1.AnalyticsDocumentType.INVOICE:
                        console.log(`🧾 Generating invoice PDF for document ${document.id}`);
                        pdfBuffer = await this.generateInvoicePdf(document);
                        break;
                    case analytics_document_request_entity_1.AnalyticsDocumentType.RECEIPT:
                        console.log(`🧾 Generating receipt PDF for document ${document.id}`);
                        pdfBuffer = await this.generateReceiptPdf(document);
                        break;
                    case analytics_document_request_entity_1.AnalyticsDocumentType.CREDIT_NOTE:
                        console.log(`🧾 Generating credit note PDF for document ${document.id}`);
                        pdfBuffer = await this.generateCreditNotePdf(document);
                        break;
                    default:
                        throw new Error(`Unsupported document type: ${request.documentType}`);
                }
                // Validate the PDF buffer before adding to array
                if (!Buffer.isBuffer(pdfBuffer)) {
                    console.log(`❌ Invalid PDF buffer for document ${document.id} (type: ${typeof pdfBuffer})`);
                    this.logger.error('Invalid PDF buffer generated', {
                        documentId: document.id,
                        bufferType: typeof pdfBuffer,
                        isBuffer: Buffer.isBuffer(pdfBuffer)
                    });
                    continue; // Skip this document
                }
                // Additional validation: check if buffer has content
                if (pdfBuffer.length === 0) {
                    console.log(`❌ Empty PDF buffer for document ${document.id}`);
                    continue;
                }
                console.log(`✅ PDF generated successfully for document ${document.id} (${pdfBuffer.length} bytes)`);
                this.logger.log('PDF generated successfully for document', {
                    documentId: document.id,
                    bufferSize: pdfBuffer.length
                });
                pdfBuffers.push(pdfBuffer);
            }
            catch (error) {
                console.log(`❌ Error generating PDF for document ${document.id}:`, error instanceof Error ? error.message : String(error));
                this.logger.error('Error generating PDF for document', {
                    documentId: document.id,
                    error: error instanceof Error ? error.message : String(error)
                });
                // Continue with other documents instead of failing completely
            }
        }
        // If no PDFs were generated successfully, return the no-data PDF
        if (pdfBuffers.length === 0) {
            console.log(`❌ No PDFs were generated successfully out of ${documentData.length} documents`);
            console.log('🔍 This indicates all individual PDF generations failed');
            console.log('📋 Generating error PDF...');
            const errorHtml = `
				<!DOCTYPE html>
				<html>
				<head>
					<style>
						body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
						h1 { color: #d32f2f; }
					</style>
				</head>
				<body>
					<h1>Error generating documents</h1>
					<p>Unable to generate PDF documents for the specified criteria.</p>
					<p>Found ${documentData.length} documents but failed to generate PDFs for all of them.</p>
				</body>
				</html>
			`;
            const errorBuffer = await (0, generatePdf_1.generatePDFBuffer)(errorHtml);
            console.log(`✅ Error PDF generated (${errorBuffer.length} bytes)`);
            console.log('=== END PDF GENERATION ===\n');
            return errorBuffer;
        }
        // Merge all PDFs into a single document
        console.log(`🔗 Merging ${pdfBuffers.length} PDFs into single document...`);
        // Log details about each PDF buffer before merging
        pdfBuffers.forEach((buffer, index) => {
            console.log(`📄 PDF ${index + 1}: ${buffer.length} bytes, header: ${buffer.slice(0, 8).toString()}`);
        });
        try {
            this.logger.log('Attempting to merge PDFs', {
                pdfCount: pdfBuffers.length,
                bufferSizes: pdfBuffers.map(buf => Buffer.isBuffer(buf) ? buf.length : 'INVALID')
            });
            // Cast Buffer[] to PDFInput[] for the mergePDFs function
            const pdfInputs = pdfBuffers;
            const mergedPdfBuffer = await (0, merge_pdf_image_into_1.mergePDFs)(pdfInputs);
            console.log(`✅ PDF merge successful! Final PDF size: ${mergedPdfBuffer.length} bytes`);
            console.log('=== END PDF GENERATION ===\n');
            this.logger.log('PDF report generated successfully', {
                documentType: request.documentType,
                documentCount: documentData.length,
                successfulPdfs: pdfBuffers.length,
                bufferSize: mergedPdfBuffer.length
            });
            return mergedPdfBuffer;
        }
        catch (mergeError) {
            console.log(`❌ PDF merge failed:`, mergeError instanceof Error ? mergeError.message : String(mergeError));
            console.log(`📊 Had ${pdfBuffers.length} individual PDFs ready for merging`);
            this.logger.error('PDF merge failed', {
                error: mergeError instanceof Error ? mergeError.message : String(mergeError),
                pdfCount: pdfBuffers.length,
                documentType: request.documentType
            });
            // Fallback: If we have only one PDF, return it directly
            if (pdfBuffers.length === 1) {
                console.log('📄 Returning single PDF as fallback');
                console.log('=== END PDF GENERATION ===\n');
                return pdfBuffers[0];
            }
            // Fallback: Try a simpler approach - create a new PDF with all content
            try {
                console.log('🔄 Attempting fallback PDF generation...');
                const fallbackHtml = `
					<!DOCTYPE html>
					<html>
					<head>
						<style>
							body { font-family: Arial, sans-serif; padding: 20px; }
							.document { page-break-after: always; margin-bottom: 50px; }
							.header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #ccc; }
						</style>
					</head>
					<body>
						<div class="header">
							<h1>${request.documentType} Report</h1>
							<p>Generated on ${new Date().toLocaleDateString()}</p>
							<p>Period: ${new Date(request.startDate).toDateString()} to ${new Date(request.endDate).toDateString()}</p>
							<p>Total Documents: ${documentData.length}</p>
						</div>
						${documentData.map((doc, index) => `
							<div class="document">
								<h3>Document ${index + 1}</h3>
								<p><strong>ID:</strong> ${doc.id}</p>
								<p><strong>Reference:</strong> ${doc.referenceAlphaId || 'N/A'}</p>
								<p><strong>Date:</strong> ${new Date(doc.createdAt).toLocaleDateString()}</p>
								<p><strong>Amount:</strong> $${doc.invoiceAmount || doc.amount || 0}</p>
							</div>
						`).join('')}
					</body>
					</html>
				`;
                const fallbackBuffer = await (0, generatePdf_1.generatePDFBuffer)(fallbackHtml);
                console.log(`✅ Fallback PDF generated successfully (${fallbackBuffer.length} bytes)`);
                console.log('=== END PDF GENERATION ===\n');
                return fallbackBuffer;
            }
            catch (fallbackError) {
                console.log(`❌ Fallback PDF generation also failed:`, fallbackError instanceof Error ? fallbackError.message : String(fallbackError));
                console.log('📋 Generating final error PDF...');
            }
            // Return a simple error PDF instead of failing completely
            const errorHtml = `
				<!DOCTYPE html>
				<html>
				<head>
					<style>
						body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
						h1 { color: #d32f2f; }
					</style>
				</head>
				<body>
					<h1>PDF Generation Error</h1>
					<p>Unable to merge PDF documents. Please contact support.</p>
					<p>Error: ${mergeError instanceof Error ? mergeError.message : 'Unknown error'}</p>
				</body>
				</html>
			`;
            return await (0, generatePdf_1.generatePDFBuffer)(errorHtml);
        }
    }
    /**
     * Generate PDF for a single invoice using professional template
     */
    async generateInvoicePdf(invoice) {
        try {
            console.log(`🧾 Generating professional invoice PDF for ID: ${invoice.id}, Amount: ${invoice.invoiceAmount}`);
            // Fetch related data for professional invoice
            const invoiceData = await this.prepareInvoiceData(invoice);
            // Generate professional invoice HTML
            const invoiceHtml = (0, generateInvoice_1.generateInvoice)(invoiceData);
            console.log(`📄 Professional invoice HTML generated (${invoiceHtml.length} characters)`);
            const buffer = await (0, generatePdf_1.generatePDFBuffer)(invoiceHtml);
            console.log(`✅ Professional invoice PDF generated successfully: ${buffer.length} bytes`);
            return buffer;
        }
        catch (error) {
            console.log(`❌ Error in generateInvoicePdf:`, error instanceof Error ? error.message : String(error));
            throw error;
        }
    }
    /**
     * Generate PDF for a single receipt using professional template
     */
    async generateReceiptPdf(receipt) {
        try {
            console.log(`🧾 Generating professional receipt PDF for ID: ${receipt.id}, Amount: ${receipt.amount}`);
            // Prepare receipt data for professional template
            const receiptData = await this.prepareReceiptData(receipt);
            // Generate professional receipt HTML
            const receiptHtml = (0, generatePaymentReceipt_1.generatePaymentReceipt)(receiptData);
            console.log(`📄 Professional receipt HTML generated (${receiptHtml.length} characters)`);
            const buffer = await (0, generatePdf_1.generatePDFBuffer)(receiptHtml);
            console.log(`✅ Professional receipt PDF generated successfully: ${buffer.length} bytes`);
            return buffer;
        }
        catch (error) {
            console.log(`❌ Error in generateReceiptPdf:`, error instanceof Error ? error.message : String(error));
            throw error;
        }
    }
    /**
     * Generate PDF for a single credit note using professional template
     */
    async generateCreditNotePdf(creditNote) {
        try {
            console.log(`🧾 Generating professional credit note PDF for ID: ${creditNote.id}, Amount: ${creditNote.amount}`);
            // Prepare credit note data for professional template
            const creditNoteData = await this.prepareCreditNoteData(creditNote);
            // Generate professional credit note HTML
            const creditNoteHtml = (0, generateCreditNote_1.generateCreditNote)(creditNoteData);
            console.log(`📄 Professional credit note HTML generated (${creditNoteHtml.length} characters)`);
            const buffer = await (0, generatePdf_1.generatePDFBuffer)(creditNoteHtml);
            console.log(`✅ Professional credit note PDF generated successfully: ${buffer.length} bytes`);
            return buffer;
        }
        catch (error) {
            console.log(`❌ Error in generateCreditNotePdf:`, error instanceof Error ? error.message : String(error));
            throw error;
        }
    }
    /**
     * Prepare invoice data for professional template
     * Uses the same pattern as send-document service
     */
    async prepareInvoiceData(invoice) {
        var _a, _b, _c;
        // Fetch patient details with necessary relations (same pattern as send-document service)
        const patientDetails = await this.patientRepository.findOne({
            where: { id: invoice.patientId },
            relations: [
                'clinic',
                'clinic.brand',
                'patientOwners',
                'patientOwners.ownerBrand',
                'patientOwners.ownerBrand.globalOwner'
            ]
        });
        if (!patientDetails) {
            throw new Error(`Patient details not found for invoice ${invoice.id}`);
        }
        const clinicDetails = patientDetails.clinic;
        if (!clinicDetails) {
            throw new Error(`Clinic details not found for invoice ${invoice.id}`);
        }
        const patientOwner = (_a = patientDetails.patientOwners) === null || _a === void 0 ? void 0 : _a[0];
        if (!(patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand)) {
            throw new Error(`Owner details not found for invoice ${invoice.id}`);
        }
        const ownerDetails = patientOwner.ownerBrand;
        // Handle clinic logo URL (same pattern as send-document service)
        let clinicLogoUrl = (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.logoUrl) || (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.clinicLogo) || '';
        // Get pre-signed URL for clinic logo if it exists and is an S3 path
        if (clinicLogoUrl && clinicLogoUrl.startsWith('clinicLogo/')) {
            try {
                const logoPreSignedUrl = await this.s3Service.getViewPreSignedUrl(clinicLogoUrl);
                clinicLogoUrl = logoPreSignedUrl;
            }
            catch (error) {
                console.log(`Warning: Could not get clinic logo URL: ${error}`);
                clinicLogoUrl = ''; // Set to empty if there's an error
            }
        }
        // Extract line items from invoice details
        const lineItems = (invoice.details || []).map((item) => ({
            description: item.name || 'Service',
            quantity: item.quantity || 1,
            price: Number(item.actualPrice) || 0
        }));
        // Prepare invoice data using the same structure as send-document service
        const invoiceData = {
            invoiceNumber: invoice.referenceAlphaId || '',
            invoiceDate: moment(invoice.createdAt).format('MMMM D, YYYY'),
            clinicName: (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.name) || '',
            clinicAddress: this.getClinicAddress(clinicDetails),
            clinicPhone: ((_c = (_b = clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.phoneNumbers) === null || _b === void 0 ? void 0 : _b[0]) === null || _c === void 0 ? void 0 : _c.number) || (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.mobile) || '',
            clinicEmail: (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.email) || '',
            clinicWebsite: (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.website) || '',
            customerName: ownerDetails ? `${ownerDetails.firstName || ''} ${ownerDetails.lastName || ''}`.trim() : 'N/A',
            petName: patientDetails.patientName || '',
            petDetails: `${patientDetails.species || ''} ${patientDetails.breed || ''}`.trim() || 'Pet',
            customerEmail: (ownerDetails === null || ownerDetails === void 0 ? void 0 : ownerDetails.email) || '',
            customerPhone: (ownerDetails === null || ownerDetails === void 0 ? void 0 : ownerDetails.globalOwner)
                ? `${ownerDetails.globalOwner.countryCode || ''}${ownerDetails.globalOwner.phoneNumber || ''}`
                : '',
            clinicLogoUrl,
            // Invoice line items
            lineItems,
            // Invoice totals
            subtotal: Number(invoice.totalPrice) || 0,
            taxes: Number(invoice.totalTax) || 0,
            discount: Number(invoice.totalDiscount) || 0,
            previousBalance: Number(invoice.totalCredit) || 0,
            invoiceAmount: Number(invoice.invoiceAmount) || 0,
            totalDue: Number(invoice.invoiceAmount) || 0,
            amountPaid: Number(invoice.amountPaid) || 0,
            balanceDue: Number(invoice.balanceDue) || 0,
            // Optional fields - set to defaults for analytics
            receiptDate: '',
            paymentMode: '',
            receiptNumber: '',
            creditsUsed: 0,
            refunds: 0,
            refundCreditNote: '',
            refundAmount: 0,
            refundDate: '',
            refundItems: [],
            paymentItems: []
        };
        return invoiceData;
    }
    /**
     * Prepare receipt data for professional template
     */
    async prepareReceiptData(receipt) {
        var _a, _b;
        // For receipts, get the full payment details with owner and clinic info
        // This matches the pattern used in send-document service
        const paymentDetails = await this.paymentDetailsRepository.findOne({
            where: { id: receipt.id },
            relations: [
                'ownerBrand',
                'patient',
                'clinic',
                'clinic.brand'
            ]
        });
        if (!paymentDetails) {
            throw new Error(`Payment details not found for receipt ${receipt.id}`);
        }
        const clinic = paymentDetails.clinic;
        const ownerBrand = paymentDetails.ownerBrand;
        // Get customer name from ownerBrand (same pattern as send-document service)
        const customerName = ownerBrand
            ? `${ownerBrand.firstName || ''} ${ownerBrand.lastName || ''}`.trim()
            : 'N/A';
        // Handle clinic logo URL (same pattern as send-document service)
        let clinicLogoUrl = (clinic === null || clinic === void 0 ? void 0 : clinic.logoUrl) || (clinic === null || clinic === void 0 ? void 0 : clinic.clinicLogo) || '';
        if (clinicLogoUrl && clinicLogoUrl.startsWith('clinicLogo/')) {
            try {
                const logoPreSignedUrl = await this.s3Service.getViewPreSignedUrl(clinicLogoUrl);
                clinicLogoUrl = logoPreSignedUrl;
            }
            catch (error) {
                console.log(`Warning: Could not get clinic logo URL: ${error}`);
                clinicLogoUrl = '';
            }
        }
        // Prepare receipt data using the same structure as send-document service
        const receiptData = {
            receiptNumber: receipt.referenceAlphaId || '',
            receiptDate: moment(receipt.createdAt).format('Do MMM YYYY'),
            clinicName: (clinic === null || clinic === void 0 ? void 0 : clinic.name) || '',
            clinicAddress: this.getClinicAddress(clinic),
            clinicPhone: ((_b = (_a = clinic === null || clinic === void 0 ? void 0 : clinic.phoneNumbers) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.number) || (clinic === null || clinic === void 0 ? void 0 : clinic.mobile) || '',
            clinicEmail: (clinic === null || clinic === void 0 ? void 0 : clinic.email) || '',
            clinicWebsite: (clinic === null || clinic === void 0 ? void 0 : clinic.website) || '',
            customerName,
            amount: Number(receipt.amount) || 0,
            paymentType: receipt.paymentType || 'Cash',
            clinicLogoUrl,
            creditsAdded: Number(receipt.creditAmountAdded) || 0,
            outstandingInvoicesPaid: [] // For analytics, we'll keep this empty for simplicity
        };
        return receiptData;
    }
    /**
     * Prepare credit note data for professional template
     * Credit notes are actually invoice entities with invoiceType === Refund
     */
    async prepareCreditNoteData(creditNote) {
        var _a, _b, _c;
        // Credit notes are invoice entities, so fetch patient with full relations
        // This matches the exact pattern used in send-document service
        const patientDetails = await this.patientRepository.findOne({
            where: { id: creditNote.patientId },
            relations: [
                'clinic',
                'clinic.brand',
                'patientOwners',
                'patientOwners.ownerBrand',
                'patientOwners.ownerBrand.globalOwner'
            ]
        });
        if (!patientDetails) {
            throw new Error(`Patient details not found for credit note ${creditNote.id}`);
        }
        const clinicDetails = patientDetails.clinic;
        if (!clinicDetails) {
            throw new Error(`Clinic details not found for credit note ${creditNote.id}`);
        }
        const patientOwner = (_a = patientDetails.patientOwners) === null || _a === void 0 ? void 0 : _a[0];
        if (!(patientOwner === null || patientOwner === void 0 ? void 0 : patientOwner.ownerBrand)) {
            throw new Error(`Owner details not found for credit note ${creditNote.id}`);
        }
        const ownerDetails = patientOwner.ownerBrand;
        // Get customer name (same pattern as send-document service)
        const customerName = `${ownerDetails.firstName || ''} ${ownerDetails.lastName || ''}`.trim();
        // Get patient info
        const petName = patientDetails.patientName || 'N/A';
        const petDetails = `${patientDetails.species || ''} ${patientDetails.breed || ''}`.trim() || 'Pet';
        // Handle clinic logo URL (same pattern as send-document service)
        let clinicLogoUrl = (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.logoUrl) || (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.clinicLogo) || '';
        if (clinicLogoUrl && clinicLogoUrl.startsWith('clinicLogo/')) {
            try {
                const logoPreSignedUrl = await this.s3Service.getViewPreSignedUrl(clinicLogoUrl);
                clinicLogoUrl = logoPreSignedUrl;
            }
            catch (error) {
                console.log(`Warning: Could not get clinic logo URL: ${error}`);
                clinicLogoUrl = '';
            }
        }
        // Get payment details for this credit note (same pattern as send-document service)
        const paymentDetails = await this.paymentDetailsRepository.find({
            where: { invoiceId: creditNote.id },
            order: { createdAt: 'ASC' }
        });
        // Get original invoice if this is a refund
        let originalInvoice = null;
        if (creditNote.invoiceType === enum_invoice_types_1.EnumInvoiceType.Refund) {
            originalInvoice = await this.invoiceRepository.findOne({
                where: {
                    cartId: creditNote.cartId,
                    invoiceType: enum_invoice_types_1.EnumInvoiceType.Invoice
                }
            });
        }
        // Find specific payment details (same pattern as send-document service)
        const creditNotePayment = paymentDetails.find(payment => payment.type === enum_credit_types_1.EnumAmountType.CreditNote);
        const collectPayment = paymentDetails.find(payment => payment.type === enum_credit_types_1.EnumAmountType.Collect && payment.isCreditsAdded);
        // Extract line items from credit note details
        const lineItems = (creditNote.details || []).map((item) => ({
            description: item.name || 'Service',
            quantity: item.quantity || 1,
            price: Number(item.actualPrice) || 0
        }));
        // Prepare credit note data using the same structure as send-document service
        const creditNoteData = {
            creditNoteNumber: creditNote.referenceAlphaId || '',
            creditNoteDate: moment(creditNote.createdAt).format('Do MMM YYYY'),
            clinicName: (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.name) || '',
            clinicAddress: this.getClinicAddress(clinicDetails),
            clinicPhone: ((_c = (_b = clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.phoneNumbers) === null || _b === void 0 ? void 0 : _b[0]) === null || _c === void 0 ? void 0 : _c.number) || (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.mobile) || '',
            clinicEmail: (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.email) || '',
            clinicWebsite: (clinicDetails === null || clinicDetails === void 0 ? void 0 : clinicDetails.website) || '',
            customerName,
            petName,
            petDetails,
            lineItems,
            adjustments: Number(creditNote.totalTax) + Number(creditNote.totalDiscount) || 0,
            totalDue: Number(creditNote.amountPayable) || 0,
            amountPaid: creditNotePayment ? Number(creditNotePayment.amount) || 0 : 0,
            balanceDue: Number(creditNote.balanceDue) || 0,
            invoiceDate: originalInvoice
                ? moment(originalInvoice.createdAt).format('MMMM D, YYYY')
                : '',
            invoiceId: originalInvoice
                ? originalInvoice.referenceAlphaId || ''
                : '',
            clinicLogoUrl,
            referenceInvoice: originalInvoice
                ? originalInvoice.referenceAlphaId || ''
                : '',
            // Optional fields (same pattern as send-document service)
            refundAmount: Number(creditNote.invoiceAmount) || 0,
            receiptDate: creditNotePayment
                ? moment(creditNotePayment.createdAt).format('Do MMM YYYY')
                : moment(creditNote.createdAt).format('Do MMM YYYY'),
            paymentMode: creditNotePayment
                ? creditNotePayment.paymentType || ''
                : creditNote.paymentMode || '',
            receiptNumber: creditNotePayment
                ? creditNotePayment.referenceAlphaId || ''
                : '',
            receiptNumberCredits: collectPayment
                ? collectPayment.referenceAlphaId || ''
                : '',
            creditsAdded: collectPayment
                ? Number(collectPayment.creditAmountAdded) || 0
                : 0
        };
        return creditNoteData;
    }
    /**
     * Get formatted clinic address (same pattern as send-document service)
     */
    getClinicAddress(clinic) {
        if (!clinic)
            return '';
        const addressParts = [
            clinic.addressLine1,
            clinic.addressLine2,
            clinic.city,
            clinic.state,
            clinic.addressPincode
        ].filter(Boolean);
        return addressParts.join(', ');
    }
    /**
     * Send analytics email with PDF and Excel attachments
     */
    async sendAnalyticsEmail(request, result) {
        try {
            // Generate S3 pre-signed URLs for file access
            const pdfUrl = result.pdfFileKey ? await this.s3Service.getViewPreSignedUrl(result.pdfFileKey) : null;
            const excelUrl = result.excelFileKey ? await this.s3Service.getViewPreSignedUrl(result.excelFileKey) : null;
            // Create email content
            const emailSubject = `Analytics Report - ${request.documentType} (${new Date(request.startDate).toDateString()} to ${new Date(request.endDate).toDateString()})`;
            const emailBody = `
				<h2>Analytics Document Report</h2>
				<p>Your requested analytics report is ready for download.</p>

				<h3>Report Details:</h3>
				<ul>
					<li><strong>Document Type:</strong> ${request.documentType}</li>
					<li><strong>Period:</strong> ${new Date(request.startDate).toDateString()} to ${new Date(request.endDate).toDateString()}</li>
					<li><strong>Documents Found:</strong> ${result.documentCount}</li>
					<li><strong>Total Size:</strong> ${Math.round(result.totalSize / 1024)} KB</li>
				</ul>

				<h3>Download Links:</h3>
				<p>Please use the links below to download your reports. These links will expire in 24 hours.</p>

				${pdfUrl ? `<p><a href="${pdfUrl}" style="color: #1976d2; text-decoration: none;">📄 Download PDF Report</a></p>` : ''}
				${excelUrl ? `<p><a href="${excelUrl}" style="color: #1976d2; text-decoration: none;">📊 Download Excel Report</a></p>` : ''}

				<hr>
				<p style="color: #666; font-size: 12px;">
					This is an automated message from Nidana Analytics System.<br>
					Report generated on ${new Date().toLocaleString()}
				</p>
			`;
            // Log the email sending (in a real implementation, you would use an email service)
            this.logger.log('📧 Analytics Email Template & URLs:', {
                requestId: request.id,
                recipientEmail: request.recipientEmail,
                documentCount: result.documentCount,
                subject: emailSubject,
                pdfDownloadUrl: pdfUrl,
                excelDownloadUrl: excelUrl,
                emailTemplate: emailBody
            });
            console.log('\n=== 📧 EMAIL TEMPLATE DEBUG ===');
            console.log('📧 TO:', request.recipientEmail);
            console.log('📧 SUBJECT:', emailSubject);
            console.log('📄 PDF URL:', pdfUrl);
            console.log('📊 EXCEL URL:', excelUrl);
            console.log('📧 EMAIL BODY:');
            console.log(emailBody);
            console.log('=== END EMAIL TEMPLATE ===\n');
            // TODO: Integrate with actual email service (SES, SendGrid, etc.)
            // Example: await this.emailService.sendEmail({
            //   to: request.recipientEmail,
            //   subject: emailSubject,
            //   html: emailBody
            // });
        }
        catch (error) {
            this.logger.error('Error sending analytics email', {
                requestId: request.id,
                recipientEmail: request.recipientEmail,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Cleanup expired analytics document requests and their associated S3 files
     * This method is called by the cron job daily
     */
    async cleanupExpiredAnalyticsDocuments() {
        const result = {
            deletedRequests: 0,
            deletedFiles: 0,
            errors: []
        };
        try {
            this.logger.log('=== 🧹 ANALYTICS DOCUMENT CLEANUP STARTED ===');
            // Find expired requests
            const expiredRequests = await this.analyticsDocumentRequestRepository.find({
                where: {
                    expiresAt: (0, typeorm_2.LessThan)(new Date())
                },
                select: ['id', 'pdfFileKey', 'excelFileKey', 'clinicId', 'documentType', 'expiresAt']
            });
            this.logger.log(`📋 Found ${expiredRequests.length} expired analytics document requests`);
            if (expiredRequests.length === 0) {
                this.logger.log('✅ No expired analytics documents to cleanup');
                return result;
            }
            // Process each expired request
            for (const request of expiredRequests) {
                try {
                    // Delete S3 files if they exist
                    const filesToDelete = [request.pdfFileKey, request.excelFileKey].filter((key) => Boolean(key));
                    for (const fileKey of filesToDelete) {
                        try {
                            await this.s3Service.deleteFile(fileKey);
                            result.deletedFiles++;
                            this.logger.log(`🗑️ Deleted S3 file: ${fileKey}`);
                        }
                        catch (fileError) {
                            const errorMsg = `Failed to delete S3 file ${fileKey}: ${fileError instanceof Error ? fileError.message : String(fileError)}`;
                            result.errors.push(errorMsg);
                            this.logger.error(errorMsg);
                        }
                    }
                    // Delete database record
                    await this.analyticsDocumentRequestRepository.delete({ id: request.id });
                    result.deletedRequests++;
                    this.logger.log(`🗑️ Deleted expired analytics request: ${request.id} (expired: ${request.expiresAt})`);
                }
                catch (requestError) {
                    const errorMsg = `Failed to cleanup request ${request.id}: ${requestError instanceof Error ? requestError.message : String(requestError)}`;
                    result.errors.push(errorMsg);
                    this.logger.error(errorMsg);
                }
            }
            this.logger.log('=== ✅ ANALYTICS DOCUMENT CLEANUP COMPLETED ===', {
                deletedRequests: result.deletedRequests,
                deletedFiles: result.deletedFiles,
                errors: result.errors.length
            });
        }
        catch (error) {
            const errorMsg = `Analytics document cleanup failed: ${error instanceof Error ? error.message : String(error)}`;
            result.errors.push(errorMsg);
            this.logger.error('=== ❌ ANALYTICS DOCUMENT CLEANUP FAILED ===', { error: errorMsg });
        }
        return result;
    }
    /**
     * Get analytics document cleanup metrics for monitoring
     */
    async getCleanupMetrics() {
        try {
            // Get total count
            const totalRequests = await this.analyticsDocumentRequestRepository.count();
            // Get expired count
            const expiredRequests = await this.analyticsDocumentRequestRepository.count({
                where: {
                    expiresAt: (0, typeorm_2.LessThan)(new Date())
                }
            });
            // Get requests by status
            const statusCounts = await this.analyticsDocumentRequestRepository
                .createQueryBuilder('request')
                .select('request.status', 'status')
                .addSelect('COUNT(*)', 'count')
                .groupBy('request.status')
                .getRawMany();
            const requestsByStatus = {};
            statusCounts.forEach(item => {
                requestsByStatus[item.status] = parseInt(item.count);
            });
            // Get oldest and newest requests
            const oldestRequest = await this.analyticsDocumentRequestRepository.findOne({
                order: { createdAt: 'ASC' },
                select: ['createdAt']
            });
            const newestRequest = await this.analyticsDocumentRequestRepository.findOne({
                order: { createdAt: 'DESC' },
                select: ['createdAt']
            });
            return {
                totalRequests,
                expiredRequests,
                requestsByStatus,
                oldestRequest: (oldestRequest === null || oldestRequest === void 0 ? void 0 : oldestRequest.createdAt) || null,
                newestRequest: (newestRequest === null || newestRequest === void 0 ? void 0 : newestRequest.createdAt) || null
            };
        }
        catch (error) {
            this.logger.error('Failed to get cleanup metrics', {
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Force cleanup of specific analytics document request
     * Useful for manual cleanup or testing
     */
    async forceCleanupRequest(requestId) {
        try {
            const request = await this.analyticsDocumentRequestRepository.findOne({
                where: { id: requestId },
                select: ['id', 'pdfFileKey', 'excelFileKey', 'clinicId', 'documentType']
            });
            if (!request) {
                this.logger.warn(`Analytics document request not found for cleanup: ${requestId}`);
                return false;
            }
            // Delete S3 files if they exist
            const filesToDelete = [request.pdfFileKey, request.excelFileKey].filter((key) => Boolean(key));
            for (const fileKey of filesToDelete) {
                try {
                    await this.s3Service.deleteFile(fileKey);
                    this.logger.log(`🗑️ Force deleted S3 file: ${fileKey}`);
                }
                catch (fileError) {
                    this.logger.error(`Failed to force delete S3 file ${fileKey}`, {
                        error: fileError instanceof Error ? fileError.message : String(fileError)
                    });
                }
            }
            // Delete database record
            await this.analyticsDocumentRequestRepository.delete({ id: requestId });
            this.logger.log(`🗑️ Force deleted analytics request: ${requestId}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to force cleanup request ${requestId}`, {
                error: error instanceof Error ? error.message : String(error)
            });
            return false;
        }
    }
};
exports.AnalyticsDocumentService = AnalyticsDocumentService;
exports.AnalyticsDocumentService = AnalyticsDocumentService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(analytics_document_request_entity_1.AnalyticsDocumentRequestEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(invoice_entity_1.InvoiceEntity)),
    __param(3, (0, typeorm_1.InjectRepository)(payment_details_entity_1.PaymentDetailsEntity)),
    __param(4, (0, typeorm_1.InjectRepository)(patient_entity_1.Patient)),
    __param(5, (0, typeorm_1.InjectRepository)(owner_brand_entity_1.OwnerBrand)),
    __param(6, (0, typeorm_1.InjectRepository)(clinic_entity_1.ClinicEntity)),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        s3_service_1.S3Service,
        users_service_1.UsersService])
], AnalyticsDocumentService);
//# sourceMappingURL=analytics-document.service.js.map