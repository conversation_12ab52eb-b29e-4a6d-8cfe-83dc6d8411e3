import { <PERSON><PERSON><PERSON><PERSON> } from '../utils/logger/winston-logger.service';
import { CreateReminderDto } from './dto/create-reminder.dto';
import { UpdateReminderDto } from './dto/update-reminder.dto';
import { ReminderStatus } from './enums/reminder.enum';
import { PatientRemindersService } from './patient-reminder.service';
export declare class PatientRemindersController {
    private readonly remindersService;
    private readonly logger;
    constructor(remindersService: PatientRemindersService, logger: WinstonLogger);
    create(patientId: string, createReminderDto: CreateReminderDto, req: {
        user: {
            brandId: string;
        };
    }): Promise<import("./entities/patient-reminder.entity").PatientReminder>;
    findAll(patientId: string, status?: ReminderStatus, page?: number, limit?: number): Promise<{
        reminders: import("./entities/patient-reminder.entity").PatientReminder[];
        total: number;
    }>;
    findOne(patientId: string, id: string): Promise<import("./entities/patient-reminder.entity").PatientReminder>;
    update(patientId: string, id: string, updateReminderDto: UpdateReminderDto): Promise<import("./entities/patient-reminder.entity").PatientReminder>;
    remove(patientId: string, id: string): Promise<{
        message: string;
    }>;
    markComplete(patientId: string, id: string): Promise<import("./entities/patient-reminder.entity").PatientReminder>;
    markOverridden(patientId: string, id: string): Promise<import("./entities/patient-reminder.entity").PatientReminder>;
    markInComplete(patientId: string, id: string): Promise<import("./entities/patient-reminder.entity").PatientReminder>;
}
