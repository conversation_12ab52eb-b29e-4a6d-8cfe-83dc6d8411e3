import { Message } from '@aws-sdk/client-sqs';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { QueueHand<PERSON> } from '../interfaces/queue-handler.interface';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../../logger/winston-logger.service';
import { AnalyticsDocumentService } from '../../../../analytics-sharing/services/analytics-document.service';

@Injectable()
export class ProcessAnalyticsDocumentsHandler implements QueueHandler {
	constructor(
		@Inject(forwardRef(() => AnalyticsDocumentService))
		private readonly analyticsDocumentService: AnalyticsDocumentService,
		private readonly logger: WinstonLogger
	) {}

	async handle(message: Message): Promise<void> {
		const body = JSON.parse(message.Body || '{}');
		const data = body.data;

		this.logger.log('Processing Analytics Documents SQS message', {
			messageId: message.MessageId,
			serviceType: data.serviceType,
			requestId: data.requestId
		});

		try {
			switch (data.serviceType) {
				case 'processAnalyticsDocuments':
					{
						const { requestId } = data;
						
						if (!requestId) {
							throw new Error('Missing requestId in analytics document processing message');
						}

						this.logger.log(`🚀 Starting analytics document processing for request: ${requestId}`);
						
						// Process the analytics documents
						await this.analyticsDocumentService.processAnalyticsDocuments(requestId);
						
						this.logger.log(`✅ Completed analytics document processing for request: ${requestId}`);
					}
					break;

				case 'cleanupExpiredDocuments':
					{
						this.logger.log('🧹 Starting analytics document cleanup');
						
						// Run cleanup for expired documents
						const result = await this.analyticsDocumentService.cleanupExpiredAnalyticsDocuments();
						
						this.logger.log('✅ Completed analytics document cleanup', {
							deletedRequests: result.deletedRequests,
							deletedFiles: result.deletedFiles,
							errors: result.errors.length
						});
					}
					break;

				default:
					this.logger.warn(`Unknown analytics service type: ${data.serviceType}`, {
						messageId: message.MessageId,
						data: JSON.stringify(data)
					});
					break;
			}

		} catch (error) {
			this.logger.error('Error processing analytics documents SQS message', {
				messageId: message.MessageId,
				serviceType: data.serviceType,
				requestId: data.requestId,
				error: error instanceof Error ? error.message : String(error),
				stack: error instanceof Error ? error.stack : undefined
			});

			// Re-throw error to trigger SQS retry mechanism
			throw error;
		}
	}
}
