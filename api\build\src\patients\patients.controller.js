"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientsController = void 0;
const common_1 = require("@nestjs/common");
const patients_service_1 = require("./patients.service");
const create_patient_dto_1 = require("./dto/create-patient.dto");
const update_patient_dto_1 = require("./dto/update-patient.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const swagger_1 = require("@nestjs/swagger");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let PatientsController = class PatientsController {
    constructor(patientsService, logger) {
        this.patientsService = patientsService;
        this.logger = logger;
    }
    async create(createPatientDto, req) {
        try {
            console.log(createPatientDto);
            this.logger.log('Creating new patient', { dto: createPatientDto });
            const patient = await this.patientsService.create(createPatientDto, req.user.brandId);
            this.logger.log('Patient created successfully', {
                patientId: patient.id
            });
            return patient;
        }
        catch (error) {
            this.logger.error('Error creating patient', { error });
            throw new common_1.HttpException('Failed to create patient', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAllPatients(page = 1, limit = 10, searchTerm = '', withBalance, patientStatus = 'all', req) {
        try {
            const withBalanceFlag = withBalance === 'true';
            this.logger.log('Fetching all patients', {
                page,
                limit,
                searchTerm
            });
            const result = await this.patientsService.getAllPatients(page, limit, searchTerm, withBalanceFlag, req.user.clinicId, patientStatus);
            this.logger.log('Patients fetched successfully');
            return result;
        }
        catch (error) {
            this.logger.error('Error fetching patients', { error });
            throw new common_1.HttpException('Failed to fetch patients', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPatient(id) {
        try {
            this.logger.log('Fetching patient details', { patientId: id });
            const patient = await this.patientsService.getPatientDetails(id);
            this.logger.log('Patient details fetched successfully', {
                patientId: id
            });
            return patient;
        }
        catch (error) {
            this.logger.error('Error fetching patient details', {
                error,
                patientId: id
            });
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to fetch patient details', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updatePatient(id, updatePatientDto) {
        try {
            this.logger.log('Updating patient', {
                patientId: id,
                dto: updatePatientDto
            });
            const result = await this.patientsService.updatePatient(id, updatePatientDto);
            if ('error' in result) {
                throw new common_1.HttpException(result.error, result.statusCode || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
            this.logger.log('Patient updated successfully', { patientId: id });
            return result;
        }
        catch (error) {
            this.logger.error('Error updating patient', {
                error,
                patientId: id
            });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to update patient', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getClinicPatients(clinicId, page = 1, limit = 10, search) {
        try {
            this.logger.log('Fetching patients for clinic', {
                clinicId,
                page,
                limit,
                search
            });
            const result = await this.patientsService.getClinicPatients(clinicId, page, limit, search);
            this.logger.log('Patients fetched successfully for clinic', {
                clinicId
            });
            return result;
        }
        catch (error) {
            this.logger.error('Error fetching patients for clinic', {
                error,
                clinicId
            });
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to fetch patients for clinic', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.PatientsController = PatientsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new patient' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'The patient has been successfully created.',
        type: create_patient_dto_1.CreatePatientDto
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, track_method_decorator_1.TrackMethod)('createPatient'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_patient_dto_1.CreatePatientDto, Object]),
    __metadata("design:returntype", Promise)
], PatientsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get all patients' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number for pagination'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Number of items per page'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'patientStatus',
        required: false,
        type: String,
        description: 'Filter patients by status (all, alive, deceased)',
        enum: ['all', 'alive', 'deceased']
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of patients with owner information'
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, track_method_decorator_1.TrackMethod)('getAllPatients'),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('searchTerm')),
    __param(3, (0, common_1.Query)('withBalance')),
    __param(4, (0, common_1.Query)('patientStatus')),
    __param(5, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String, String, Object]),
    __metadata("design:returntype", Promise)
], PatientsController.prototype, "getAllPatients", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get a patient by id' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', description: 'Patient ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The patient details',
        type: create_patient_dto_1.CreatePatientDto
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Patient not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, track_method_decorator_1.TrackMethod)('getPatientById'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PatientsController.prototype, "getPatient", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Update a patient' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: 'string', description: 'Patient ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'The patient has been successfully updated.',
        type: update_patient_dto_1.UpdatePatientDTO
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Patient not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, track_method_decorator_1.TrackMethod)('updatePatient'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_patient_dto_1.UpdatePatientDTO]),
    __metadata("design:returntype", Promise)
], PatientsController.prototype, "updatePatient", null);
__decorate([
    (0, common_1.Get)('clinic/:clinicId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get all patients for a specific clinic' }),
    (0, swagger_1.ApiParam)({
        name: 'clinicId',
        type: 'string',
        description: 'ID of the clinic'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number for pagination'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Number of items per page'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'search',
        required: false,
        type: String,
        description: 'Search patient by name, or owner info'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of patients for the specified clinic'
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Clinic not found.' }),
    (0, track_method_decorator_1.TrackMethod)('getClinicPatients'),
    __param(0, (0, common_1.Param)('clinicId')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __param(3, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number, String]),
    __metadata("design:returntype", Promise)
], PatientsController.prototype, "getClinicPatients", null);
exports.PatientsController = PatientsController = __decorate([
    (0, swagger_1.ApiTags)('Patients'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('patients'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [patients_service_1.PatientsService,
        winston_logger_service_1.WinstonLogger])
], PatientsController);
//# sourceMappingURL=patients.controller.js.map