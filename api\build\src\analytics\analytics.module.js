"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const analytics_controller_1 = require("./analytics.controller");
const analytics_service_1 = require("./analytics.service");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const payment_details_entity_1 = require("../payment-details/entities/payment-details.entity");
const patient_entity_1 = require("../patients/entities/patient.entity");
const owner_brand_entity_1 = require("../owners/entities/owner-brand.entity");
const role_module_1 = require("../roles/role.module");
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
const clinic_entity_1 = require("../clinics/entities/clinic.entity");
const analytics_document_request_entity_1 = require("../analytics-sharing/entities/analytics-document-request.entity");
const analytics_document_service_1 = require("../analytics-sharing/services/analytics-document.service");
const sqs_service_1 = require("../utils/aws/sqs/sqs.service");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const users_module_1 = require("../users/users.module");
let AnalyticsModule = class AnalyticsModule {
};
exports.AnalyticsModule = AnalyticsModule;
exports.AnalyticsModule = AnalyticsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                invoice_entity_1.InvoiceEntity,
                payment_details_entity_1.PaymentDetailsEntity,
                patient_entity_1.Patient,
                owner_brand_entity_1.OwnerBrand,
                appointment_entity_1.AppointmentEntity,
                clinic_entity_1.ClinicEntity,
                analytics_document_request_entity_1.AnalyticsDocumentRequestEntity
            ]),
            role_module_1.RoleModule,
            users_module_1.UsersModule
        ],
        controllers: [analytics_controller_1.AnalyticsController],
        providers: [
            analytics_service_1.AnalyticsService,
            analytics_document_service_1.AnalyticsDocumentService,
            sqs_service_1.SqsService,
            winston_logger_service_1.WinstonLogger,
            s3_service_1.S3Service
        ],
        exports: [analytics_service_1.AnalyticsService]
    })
], AnalyticsModule);
//# sourceMappingURL=analytics.module.js.map