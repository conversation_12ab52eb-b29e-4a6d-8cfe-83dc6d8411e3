import { PaymentDetailsService } from './payment-details.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { PaymentDetailsEntity } from './entities/payment-details.entity';
import { PaymentDetailsDto } from './dto/payment-details.dto';
import { EnumInvoiceStatus } from '../invoice/enums/enum-invoice-status';
import { BulkPaymentDetailsDto } from './dto/bulk-payment-details.dto';
import { BulkPaymentResponse } from './dto/bulk-payment-response.dto';
import { TabActivitiesService } from '../tab-activity/tab-activity.service';
import { EditPaymentDetailsDto } from './dto/edit-payment-details.dto';
import { DeletePaymentDetailsDto } from './dto/delete-payment-details.dto';
import { LedgerItem } from './interfaces/ledger-item.interface';
export declare class PaymentDetailsController {
    private readonly logger;
    private readonly paymentDetailsService;
    private readonly tabActivitiesService;
    constructor(logger: WinstonLogger, paymentDetailsService: PaymentDetailsService, tabActivitiesService: TabActivitiesService);
    createPaymentDetails(paymentDetailsDto: PaymentDetailsDto, req: {
        user: {
            clinicId: string;
            brandId: string;
            userId: string;
        };
    }): Promise<PaymentDetailsEntity[]>;
    getPaymentDetailsForPatientsOwner(patientId: string): Promise<{
        paymentDetails: PaymentDetailsEntity[];
        total: number;
        ownerDetails: {
            createdAt: Date;
            openingBalance: number;
        } | null;
    }>;
    getPaymentDetailsForPatient(patientId: string, search?: string, page?: string, limit?: string): Promise<{
        paymentDetails: PaymentDetailsEntity[];
        total: number;
        ownerDetails: {
            createdAt: Date;
            openingBalance: number;
        } | null;
        totalPages: number;
        currentPage: number;
        uniqueUsers: {
            id: string;
            name: string;
        }[];
    }>;
    getInvoicesForPatient(patientId: string, search?: string, page?: string, limit?: string, invoiceType?: string): Promise<import("./payment-details.service").PatientInvoicesResponse>;
    getPaymentDetailsForOwner(ownerId: string, startDate?: string, endDate?: string, petName?: string, paymentMode?: string, paymentType?: string, userId?: string, searchTerm?: string, page?: string, limit?: string): Promise<{
        paymentDetails: PaymentDetailsEntity[];
        total: number;
        ownerDetails: {
            createdAt: Date;
            openingBalance: number;
        } | null;
        uniqueUsers: {
            id: string;
            name: string;
        }[];
    }>;
    getIndividualPaymentDetail(id: string): Promise<PaymentDetailsEntity>;
    deleteLegderFileKeyForPayment(id: string): Promise<void>;
    getOwnerInvoicesWithPayments(ownerId: string, req: {
        user: {
            userId: string;
            brandId: string;
            clinicId: string;
        };
    }, page?: string, limit?: string, startDate?: string, endDate?: string, petName?: string, status?: string, paymentMode?: string, searchTerm?: string, userId?: string, invoiceType?: string): Promise<import("./dto/owner-invoice-response.dto").OwnerInvoiceResponse>;
    getOwnerPendingInvoices(ownerId: string, req: {
        user: {
            userId: string;
            brandId: string;
            clinicId: string;
        };
    }, startDate?: string, endDate?: string, petName?: string, searchTerm?: string): Promise<{
        ownerDetails: {
            id: string;
            name: string;
            balance: number;
            credits: number;
        };
        pendingInvoices: {
            id: string;
            invoiceDate: Date;
            metadata: Record<string, any>;
            patientId: string;
            patientName: string;
            invoiceReference: string;
            balanceDue: number;
            invoiceAmount: number;
            totalCollected: number;
            comment: string;
            status: EnumInvoiceStatus;
        }[];
    }>;
    getOwnerLedger(ownerId: string, req: {
        user: {
            userId: string;
            brandId: string;
            clinicId: string;
        };
    }): Promise<{
        ownerDetails: {
            id: string;
            name: string;
            balance: number;
            credits: number;
            createdAt: Date;
            openingBalance: number;
        };
        ledgerItems: LedgerItem[];
        uniqueUsers: {
            id: string;
            name: string;
        }[];
        summary: {
            totalMonetaryDebits: number;
            totalMonetaryCredits: number;
            finalRunningBalance: number;
            totalProfileCreditsAdded: number;
            totalProfileCreditsUsed: number;
            finalRunningCredits: number;
        };
    }>;
    createBulkPaymentDetails(bulkPaymentDetailsDto: BulkPaymentDetailsDto, req: {
        user: {
            clinicId: string;
            brandId: string;
            userId: string;
        };
    }): Promise<BulkPaymentResponse>;
    checkPaymentDocumentStatus(referenceAlphaId: string, req: {
        user: {
            clinicId: string;
            brandId: string;
            userId: string;
        };
    }): Promise<{
        status: boolean;
        data: {
            isReady: boolean;
            url?: undefined;
            fileName?: undefined;
            isPermanent?: undefined;
        };
        message: string;
    } | {
        status: boolean;
        data: {
            isReady: boolean;
            url: string;
            fileName: string | undefined;
            isPermanent: boolean;
        };
        message: string;
    }>;
    handlePaymentDocument(referenceAlphaId: string, documentType: 'payment-details', action: 'share' | 'download', shareMethod: 'email' | 'whatsapp' | 'both' | undefined, recipient: 'client' | 'other' | undefined, email: string | undefined, phoneNumber: string | undefined, req: {
        user: {
            clinicId: string;
            brandId: string;
            userId: string;
        };
    }): Promise<{
        status: string;
        data: {
            downloadUrl: string;
            fileName: string;
            referenceAlphaId?: undefined;
            message?: undefined;
        };
    } | {
        status: string;
        data: {
            referenceAlphaId: string;
            message: string;
            downloadUrl?: undefined;
            fileName?: undefined;
        };
    }>;
    editPaymentDetails(id: string, editDto: EditPaymentDetailsDto, req: {
        user: {
            userId: string;
            brandId: string;
            clinicId: string;
        };
    }): Promise<PaymentDetailsEntity>;
    deletePaymentDetails(id: string, deleteDto: DeletePaymentDetailsDto, req: {
        user: {
            userId: string;
            brandId: string;
            clinicId: string;
        };
    }): Promise<{
        message: string;
    }>;
}
