"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OwnersModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const owners_controller_1 = require("./owners.controller");
const owners_service_1 = require("./owners.service");
const global_owner_entity_1 = require("./entities/global-owner.entity");
const owner_brand_entity_1 = require("./entities/owner-brand.entity");
const patient_entity_1 = require("../patients/entities/patient.entity");
const patient_owner_entity_1 = require("../patients/entities/patient-owner.entity");
const role_module_1 = require("../roles/role.module");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const brand_entity_1 = require("../brands/entities/brand.entity");
const whatsapp_module_1 = require("../utils/whatsapp-integration/whatsapp.module");
const ses_module_1 = require("../utils/aws/ses/ses.module");
const pet_transfer_history_entity_1 = require("./entities/pet-transfer-history.entity");
const payment_details_entity_1 = require("../payment-details/entities/payment-details.entity");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
let OwnersModule = class OwnersModule {
};
exports.OwnersModule = OwnersModule;
exports.OwnersModule = OwnersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                global_owner_entity_1.GlobalOwner,
                owner_brand_entity_1.OwnerBrand,
                patient_entity_1.Patient,
                patient_owner_entity_1.PatientOwner,
                brand_entity_1.Brand,
                pet_transfer_history_entity_1.PetTransferHistory,
                payment_details_entity_1.PaymentDetailsEntity,
                invoice_entity_1.InvoiceEntity
            ]),
            role_module_1.RoleModule,
            whatsapp_module_1.WhatsappModule,
            ses_module_1.SESModule
        ],
        controllers: [owners_controller_1.OwnersController],
        providers: [owners_service_1.OwnersService, winston_logger_service_1.WinstonLogger],
        exports: [owners_service_1.OwnersService]
    })
], OwnersModule);
//# sourceMappingURL=owners.module.js.map