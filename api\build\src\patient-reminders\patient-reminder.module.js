"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientRemindersModule = void 0;
// src/patient-reminders/patient-reminders.module.ts
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const patient_reminder_entity_1 = require("./entities/patient-reminder.entity");
const patient_reminder_history_entity_1 = require("./entities/patient-reminder-history.entity");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const patient_reminder_controller_1 = require("./patient-reminder.controller");
const patient_reminder_service_1 = require("./patient-reminder.service");
const role_module_1 = require("../roles/role.module");
const global_reminders_module_1 = require("../patient-global-reminders/global-reminders.module");
const ses_module_1 = require("../utils/aws/ses/ses.module");
const whatsapp_module_1 = require("../utils/whatsapp-integration/whatsapp.module");
let PatientRemindersModule = class PatientRemindersModule {
};
exports.PatientRemindersModule = PatientRemindersModule;
exports.PatientRemindersModule = PatientRemindersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([patient_reminder_entity_1.PatientReminder, patient_reminder_history_entity_1.PatientReminderHistory]),
            role_module_1.RoleModule,
            global_reminders_module_1.GlobalReminderModule,
            ses_module_1.SESModule,
            whatsapp_module_1.WhatsappModule
        ],
        controllers: [patient_reminder_controller_1.PatientRemindersController],
        providers: [patient_reminder_service_1.PatientRemindersService, winston_logger_service_1.WinstonLogger],
        exports: [patient_reminder_service_1.PatientRemindersService]
    })
], PatientRemindersModule);
//# sourceMappingURL=patient-reminder.module.js.map