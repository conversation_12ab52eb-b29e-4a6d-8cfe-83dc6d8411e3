"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var StatementController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatementController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger"); // For API documentation
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard"); // Assuming JWT auth
const roles_guard_1 = require("../auth/guards/roles.guard"); // Assuming Roles guard
const roles_decorator_1 = require("../roles/roles.decorator"); // Assuming Roles decorator
const role_enum_1 = require("../roles/role.enum"); // Assuming Role enum
const statement_service_1 = require("./statement.service");
const statement_document_entity_1 = require("./entities/statement-document.entity");
let StatementController = StatementController_1 = class StatementController {
    constructor(statementService) {
        this.statementService = statementService;
        this.logger = new common_1.Logger(StatementController_1.name);
    } // Corrected: service injected
    async requestStatementGeneration(ownerId, types, action, statementIds, req, shareMethod, recipient, email, phoneNumber, filterUserId, filterStatus, filterStartDate, filterEndDate, filterSearchTerm, filterPaymentMode) {
        this.logger.log(`Request to generate statements for owner ${ownerId}, types: ${types}, action: ${action}`);
        if (!ownerId || !types || !action) {
            throw new common_1.HttpException('Missing required parameters: ownerId, types, or action.', common_1.HttpStatus.BAD_REQUEST);
        }
        const requestedTypes = types
            .split(',')
            .map(t => t.trim())
            .filter(t => t);
        if (requestedTypes.length === 0) {
            throw new common_1.HttpException('No valid statement types provided.', common_1.HttpStatus.BAD_REQUEST);
        }
        // Basic validation for 'types'
        const validTypes = [
            'invoice',
            'refund',
            'payment-detail',
            'credit-statement'
        ];
        for (const type of requestedTypes) {
            if (!validTypes.includes(type)) {
                throw new common_1.HttpException(`Invalid statement type: ${type}. Valid types are ${validTypes.join(', ')}.`, common_1.HttpStatus.BAD_REQUEST);
            }
        }
        if (action === statement_document_entity_1.StatementAction.SHARE && !shareMethod) {
            throw new common_1.HttpException('Share method is required for share action.', common_1.HttpStatus.BAD_REQUEST);
        }
        if (action === statement_document_entity_1.StatementAction.SHARE &&
            recipient === 'other' &&
            !email &&
            !phoneNumber) {
            throw new common_1.HttpException("Email or phone number is required for sharing with recipient type 'other'.", common_1.HttpStatus.BAD_REQUEST);
        }
        const shareDetailsDto = action === statement_document_entity_1.StatementAction.SHARE
            ? {
                shareMethod: shareMethod,
                recipient: recipient,
                email,
                phoneNumber
            }
            : undefined;
        const userContext = {
            clinicId: req.user.clinicId,
            brandId: req.user.brandId,
            userId: req.user.userId
        };
        // Prepare filter parameters
        const filters = {
            userId: filterUserId,
            status: filterStatus,
            startDate: filterStartDate,
            endDate: filterEndDate,
            searchTerm: filterSearchTerm,
            paymentMode: filterPaymentMode
        };
        const requestId = await this.statementService.requestStatementGeneration(ownerId, requestedTypes, action, shareDetailsDto, userContext, filters, statementIds);
        return {
            requestId,
            status: 'processing'
        };
    }
    async getStatementDocumentStatus(requestId, req // Added for logging/auth context if needed
    ) {
        this.logger.log(`Checking status for statement request ${requestId}, user: ${req.user.userId}`);
        if (!requestId) {
            throw new common_1.HttpException('Missing required parameter: requestId.', common_1.HttpStatus.BAD_REQUEST);
        }
        const result = await this.statementService.getStatementDocumentStatus(requestId);
        return result;
    }
};
exports.StatementController = StatementController;
__decorate([
    (0, common_1.Post)('owner/:ownerId/documents'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.RECEPTIONIST, role_enum_1.Role.DOCTOR) // Define appropriate roles
    ,
    (0, swagger_1.ApiOperation)({
        summary: 'Request generation of owner statements (invoice, refund, payment-detail, credit-statement)'
    }),
    (0, swagger_1.ApiParam)({
        name: 'ownerId',
        type: 'string',
        description: 'ID of the owner'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'types',
        type: 'string',
        description: 'Comma-separated statement types (e.g., invoice,refund,payment-detail,credit-statement)',
        example: 'invoice,payment-detail,credit-statement'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'action',
        enum: statement_document_entity_1.StatementAction,
        description: 'Action to perform: download or share'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'shareMethod',
        enum: ['email', 'whatsapp', 'both'],
        required: false,
        description: 'Method for sharing (if action is share)'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'recipient',
        enum: ['client', 'other'],
        required: false,
        description: 'Recipient for sharing (if action is share)'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'email',
        type: 'string',
        required: false,
        description: 'Custom email for sharing (if recipient is other)'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'phoneNumber',
        type: 'string',
        required: false,
        description: 'Custom phone number for sharing (if recipient is other)'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'userId',
        type: 'string',
        required: false,
        description: 'Filter by user ID'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'status',
        type: 'string',
        required: false,
        description: 'Filter by status (comma-separated list supported)'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'startDate',
        type: 'string',
        required: false,
        description: 'Filter by start date (YYYY-MM-DD format)'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'endDate',
        type: 'string',
        required: false,
        description: 'Filter by end date (YYYY-MM-DD format)'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'searchTerm',
        type: 'string',
        required: false,
        description: 'Search term for filtering'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'paymentMode',
        type: 'string',
        required: false,
        description: 'Filter by payment mode (comma-separated list supported)'
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Statement generation request accepted.',
        type: Object // Changed to Object as any for DTO placeholder
    }),
    __param(0, (0, common_1.Param)('ownerId')),
    __param(1, (0, common_1.Query)('types')),
    __param(2, (0, common_1.Query)('action')),
    __param(3, (0, common_1.Body)()),
    __param(4, (0, common_1.Req)()),
    __param(5, (0, common_1.Query)('shareMethod')),
    __param(6, (0, common_1.Query)('recipient')),
    __param(7, (0, common_1.Query)('email')),
    __param(8, (0, common_1.Query)('phoneNumber')),
    __param(9, (0, common_1.Query)('userId')),
    __param(10, (0, common_1.Query)('status')),
    __param(11, (0, common_1.Query)('startDate')),
    __param(12, (0, common_1.Query)('endDate')),
    __param(13, (0, common_1.Query)('searchTerm')),
    __param(14, (0, common_1.Query)('paymentMode')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Array, Object, String, String, String, String, String, String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], StatementController.prototype, "requestStatementGeneration", null);
__decorate([
    (0, common_1.Get)('documents/status/:requestId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.RECEPTIONIST, role_enum_1.Role.DOCTOR) // Define appropriate roles
    ,
    (0, swagger_1.ApiOperation)({
        summary: 'Get the status of a statement generation request and download links if ready.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'requestId',
        type: 'string',
        description: 'The request ID obtained from the generation request.'
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Status of the statement generation.',
        type: Object
    }) // Update Type when DTO is solid
    ,
    __param(0, (0, common_1.Param)('requestId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], StatementController.prototype, "getStatementDocumentStatus", null);
exports.StatementController = StatementController = StatementController_1 = __decorate([
    (0, swagger_1.ApiTags)('Statements'),
    (0, common_1.Controller)('statements') // Base path for statement-related endpoints
    ,
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard) // Apply guards at controller level
    ,
    __metadata("design:paramtypes", [statement_service_1.StatementService])
], StatementController);
//# sourceMappingURL=statement.controller.js.map