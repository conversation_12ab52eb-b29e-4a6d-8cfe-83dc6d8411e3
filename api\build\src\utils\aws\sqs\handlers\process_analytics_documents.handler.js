"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessAnalyticsDocumentsHandler = void 0;
const common_1 = require("@nestjs/common");
const winston_logger_service_1 = require("../../../logger/winston-logger.service");
const analytics_document_service_1 = require("../../../../analytics-sharing/services/analytics-document.service");
let ProcessAnalyticsDocumentsHandler = class ProcessAnalyticsDocumentsHandler {
    constructor(analyticsDocumentService, logger) {
        this.analyticsDocumentService = analyticsDocumentService;
        this.logger = logger;
    }
    async handle(message) {
        const body = JSON.parse(message.Body || '{}');
        const data = body.data;
        this.logger.log('Processing Analytics Documents SQS message', {
            messageId: message.MessageId,
            serviceType: data.serviceType,
            requestId: data.requestId
        });
        try {
            switch (data.serviceType) {
                case 'processAnalyticsDocuments':
                    {
                        const { requestId } = data;
                        if (!requestId) {
                            throw new Error('Missing requestId in analytics document processing message');
                        }
                        this.logger.log(`🚀 Starting analytics document processing for request: ${requestId}`);
                        // Process the analytics documents
                        await this.analyticsDocumentService.processAnalyticsDocuments(requestId);
                        this.logger.log(`✅ Completed analytics document processing for request: ${requestId}`);
                    }
                    break;
                case 'cleanupExpiredDocuments':
                    {
                        this.logger.log('🧹 Starting analytics document cleanup');
                        // Run cleanup for expired documents
                        const result = await this.analyticsDocumentService.cleanupExpiredAnalyticsDocuments();
                        this.logger.log('✅ Completed analytics document cleanup', {
                            deletedRequests: result.deletedRequests,
                            deletedFiles: result.deletedFiles,
                            errors: result.errors.length
                        });
                    }
                    break;
                default:
                    this.logger.warn(`Unknown analytics service type: ${data.serviceType}`, {
                        messageId: message.MessageId,
                        data: JSON.stringify(data)
                    });
                    break;
            }
        }
        catch (error) {
            this.logger.error('Error processing analytics documents SQS message', {
                messageId: message.MessageId,
                serviceType: data.serviceType,
                requestId: data.requestId,
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined
            });
            // Re-throw error to trigger SQS retry mechanism
            throw error;
        }
    }
};
exports.ProcessAnalyticsDocumentsHandler = ProcessAnalyticsDocumentsHandler;
exports.ProcessAnalyticsDocumentsHandler = ProcessAnalyticsDocumentsHandler = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)((0, common_1.forwardRef)(() => analytics_document_service_1.AnalyticsDocumentService))),
    __metadata("design:paramtypes", [analytics_document_service_1.AnalyticsDocumentService,
        winston_logger_service_1.WinstonLogger])
], ProcessAnalyticsDocumentsHandler);
//# sourceMappingURL=process_analytics_documents.handler.js.map