"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentDetailsModule = void 0;
const common_1 = require("@nestjs/common");
const payment_details_service_1 = require("./payment-details.service");
const payment_details_controller_1 = require("./payment-details.controller");
const typeorm_1 = require("@nestjs/typeorm");
const payment_details_entity_1 = require("./entities/payment-details.entity");
const patient_entity_1 = require("../patients/entities/patient.entity");
const patients_service_1 = require("../patients/patients.service");
const patient_owner_entity_1 = require("../patients/entities/patient-owner.entity");
const clinic_entity_1 = require("../clinics/entities/clinic.entity");
const owners_module_1 = require("../owners/owners.module");
const role_module_1 = require("../roles/role.module");
const owners_service_1 = require("../owners/owners.service");
const ses_module_1 = require("../utils/aws/ses/ses.module");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const whatsapp_module_1 = require("../utils/whatsapp-integration/whatsapp.module");
const global_reminders_module_1 = require("../patient-global-reminders/global-reminders.module");
const global_owner_entity_1 = require("../owners/entities/global-owner.entity");
const owner_brand_entity_1 = require("../owners/entities/owner-brand.entity");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const invoice_audit_log_entity_1 = require("../invoice/entities/invoice-audit-log.entity");
const brand_entity_1 = require("../brands/entities/brand.entity");
const pet_transfer_history_entity_1 = require("../owners/entities/pet-transfer-history.entity");
const credit_transaction_entity_1 = require("../credits/entities/credit-transaction.entity");
const credits_module_1 = require("../credits/credits.module");
const sqs_module_1 = require("../utils/aws/sqs/sqs.module");
const sqs_service_1 = require("../utils/aws/sqs/sqs.service");
const tab_activity_module_1 = require("../tab-activity/tab-activity.module");
const tab_activity_entity_1 = require("../tab-activity/entities/tab-activity.entity");
const merged_payment_receipt_document_entity_1 = require("./entities/merged-payment-receipt-document.entity");
const payment_details_audit_log_entity_1 = require("./entities/payment-details-audit-log.entity");
const logger_module_1 = require("../utils/logger/logger-module");
const s3_module_1 = require("../utils/aws/s3/s3.module");
let PaymentDetailsModule = class PaymentDetailsModule {
};
exports.PaymentDetailsModule = PaymentDetailsModule;
exports.PaymentDetailsModule = PaymentDetailsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                payment_details_entity_1.PaymentDetailsEntity,
                payment_details_audit_log_entity_1.PaymentDetailsAuditLogEntity,
                patient_entity_1.Patient,
                patient_owner_entity_1.PatientOwner,
                clinic_entity_1.ClinicEntity,
                global_owner_entity_1.GlobalOwner,
                owner_brand_entity_1.OwnerBrand,
                invoice_entity_1.InvoiceEntity,
                invoice_audit_log_entity_1.InvoiceAuditLogEntity,
                brand_entity_1.Brand,
                pet_transfer_history_entity_1.PetTransferHistory,
                credit_transaction_entity_1.CreditTransactionEntity,
                tab_activity_entity_1.TabActivityEntity,
                merged_payment_receipt_document_entity_1.MergedPaymentReceiptDocumentEntity
            ]),
            owners_module_1.OwnersModule,
            ses_module_1.SESModule,
            whatsapp_module_1.WhatsappModule,
            global_reminders_module_1.GlobalReminderModule,
            role_module_1.RoleModule,
            credits_module_1.CreditsModule,
            (0, common_1.forwardRef)(() => sqs_module_1.SqsModule),
            tab_activity_module_1.TabActivityModule,
            logger_module_1.LoggerModule,
            s3_module_1.S3Module
        ],
        controllers: [payment_details_controller_1.PaymentDetailsController],
        providers: [
            payment_details_service_1.PaymentDetailsService,
            patients_service_1.PatientsService,
            owners_service_1.OwnersService,
            s3_service_1.S3Service,
            sqs_service_1.SqsService
        ],
        exports: [payment_details_service_1.PaymentDetailsService]
    })
], PaymentDetailsModule);
//# sourceMappingURL=payment-details.module.js.map