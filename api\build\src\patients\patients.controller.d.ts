import { PatientsService } from './patients.service';
import { CreatePatientDto } from './dto/create-patient.dto';
import { UpdatePatientDTO } from './dto/update-patient.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
export declare class PatientsController {
    private readonly patientsService;
    private readonly logger;
    constructor(patientsService: PatientsService, logger: WinstonLogger);
    create(createPatientDto: CreatePatientDto, req: {
        user: {
            brandId: string;
        };
    }): Promise<import("./entities/patient.entity").Patient>;
    getAllPatients(page: number | undefined, limit: number | undefined, searchTerm: string | undefined, withBalance: string, patientStatus: string | undefined, req: {
        user: {
            clinicId: string;
        };
    }): Promise<{
        patients: any[];
        total: number;
    }>;
    getPatient(id: string): Promise<import("./entities/patient.entity").Patient>;
    updatePatient(id: string, updatePatientDto: UpdatePatientDTO): Promise<import("./entities/patient.entity").Patient>;
    getClinicPatients(clinicId: string, page: number | undefined, limit: number | undefined, search: string): Promise<{
        patients: any[];
        total: number;
    }>;
}
