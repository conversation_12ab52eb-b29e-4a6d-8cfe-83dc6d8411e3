import { StatementService } from './statement.service';
import { StatementAction, StatementDocumentStatus } from './entities/statement-document.entity';
interface RequestStatementResponseDto {
    requestId: string;
    status: 'processing';
}
interface StatementStatusResponseDto {
    status: StatementDocumentStatus;
    documents?: Record<string, {
        url: string;
        fileName: string;
    }>;
    errorMessage?: string | null;
    requestId: string;
}
export declare class StatementController {
    private readonly statementService;
    private readonly logger;
    constructor(statementService: StatementService);
    requestStatementGeneration(ownerId: string, types: string, action: StatementAction, statementIds: string[], req: {
        user: {
            clinicId: string;
            brandId: string;
            userId: string;
        };
    }, shareMethod?: 'email' | 'whatsapp' | 'both', recipient?: 'client' | 'other', email?: string, phoneNumber?: string, filterUserId?: string, filterStatus?: string, filterStartDate?: string, filterEndDate?: string, filterSearchTerm?: string, filterPaymentMode?: string): Promise<RequestStatementResponseDto>;
    getStatementDocumentStatus(requestId: string, req: {
        user: {
            userId: string;
        };
    }): Promise<StatementStatusResponseDto>;
}
export {};
