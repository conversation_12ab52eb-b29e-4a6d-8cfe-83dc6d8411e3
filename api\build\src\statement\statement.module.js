"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatementModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const statement_document_entity_1 = require("./entities/statement-document.entity");
const statement_controller_1 = require("./statement.controller");
const statement_service_1 = require("./statement.service");
// SqsService and S3Service will be provided by their modules
const payment_details_module_1 = require("../payment-details/payment-details.module");
const owners_module_1 = require("../owners/owners.module");
const patients_module_1 = require("../patients/patients.module");
const send_document_service_1 = require("../utils/common/send-document.service");
const whatsapp_module_1 = require("../utils/whatsapp-integration/whatsapp.module");
const brands_module_1 = require("../brands/brands.module");
const clinic_module_1 = require("../clinics/clinic.module");
// BrandService and ClinicService will be provided by their modules
const sqs_module_1 = require("../utils/aws/sqs/sqs.module"); // Import SqsModule
const s3_module_1 = require("../utils/aws/s3/s3.module"); // Import S3Module
const ses_module_1 = require("../utils/aws/ses/ses.module"); // ADDED IMPORT
const role_module_1 = require("../roles/role.module"); // ADDED IMPORT for RolesGuard
const credits_module_1 = require("../credits/credits.module"); // Import CreditsModule
// Removed direct service imports as they are provided by their respective modules
// import { SqsService } from '../utils/aws/sqs/sqs.service';
// import { S3Service } from '../utils/aws/s3/s3.service';
// import { BrandService } from '../brands/brands.service';
// import { ClinicService } from '../clinics/clinic.service';
// Entities for SendDocuments
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
const merged_payment_receipt_document_entity_1 = require("../payment-details/entities/merged-payment-receipt-document.entity");
const patient_vaccinations_entity_1 = require("../patient-vaccinations/entities/patient-vaccinations.entity");
const patient_entity_1 = require("../patients/entities/patient.entity");
const emr_entity_1 = require("../emr/entities/emr.entity");
const payment_details_entity_1 = require("../payment-details/entities/payment-details.entity");
const lab_report_entity_1 = require("../clinic-lab-report/entities/lab-report.entity");
const appointment_details_entity_1 = require("../appointments/entities/appointment-details.entity");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const appointment_doctor_entity_1 = require("../appointments/entities/appointment-doctor.entity");
const merged_invoice_document_entity_1 = require("../invoice/entities/merged-invoice-document.entity");
const tab_activity_module_1 = require("../tab-activity/tab-activity.module");
let StatementModule = class StatementModule {
};
exports.StatementModule = StatementModule;
exports.StatementModule = StatementModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                statement_document_entity_1.StatementDocumentEntity,
                // Entities required by SendDocuments
                appointment_entity_1.AppointmentEntity,
                merged_payment_receipt_document_entity_1.MergedPaymentReceiptDocumentEntity,
                patient_vaccinations_entity_1.PatientVaccination,
                patient_entity_1.Patient, // Patient might already be covered if PatientsModule exports TypeOrm.forFeature([Patient])
                emr_entity_1.Emr,
                payment_details_entity_1.PaymentDetailsEntity, // May be covered by PaymentDetailsModule
                lab_report_entity_1.LabReport,
                appointment_details_entity_1.AppointmentDetailsEntity,
                invoice_entity_1.InvoiceEntity, // May be covered by InvoiceModule if StatementModule imports it for PaymentDetailsService etc.
                appointment_doctor_entity_1.AppointmentDoctorsEntity,
                merged_invoice_document_entity_1.MergedInvoiceDocumentEntity
            ]),
            (0, common_1.forwardRef)(() => payment_details_module_1.PaymentDetailsModule), // Provides PaymentDetailsService, possibly some entities
            owners_module_1.OwnersModule, // Provides OwnersService
            patients_module_1.PatientsModule, // Provides PatientsService, Patient entity
            whatsapp_module_1.WhatsappModule, // For SendDocuments
            brands_module_1.BrandsModule, // Provides BrandService
            (0, common_1.forwardRef)(() => clinic_module_1.ClinicModule), // Provides ClinicService
            (0, common_1.forwardRef)(() => sqs_module_1.SqsModule), // Provides SqsService
            s3_module_1.S3Module, // For S3Service
            ses_module_1.SESModule, // ADDED: For mailService in SendDocuments
            role_module_1.RoleModule, // ADDED: For RolesGuard
            tab_activity_module_1.TabActivityModule, // ADDED: For TabActivitiesService in SendDocuments
            (0, common_1.forwardRef)(() => credits_module_1.CreditsModule) // Added: For CreditsService
            // HttpModule, // If SendDocumentsService uses HttpService from @nestjs/axios
        ],
        controllers: [statement_controller_1.StatementController],
        providers: [
            statement_service_1.StatementService,
            send_document_service_1.SendDocuments // SendDocuments seems to be a standalone utility service
            // SqsService, S3Service, BrandService, ClinicService are removed from here.
            // They will be available via their imported modules.
        ],
        exports: [statement_service_1.StatementService] // StatementService is exported as SqsModule depends on it.
    })
], StatementModule);
//# sourceMappingURL=statement.module.js.map