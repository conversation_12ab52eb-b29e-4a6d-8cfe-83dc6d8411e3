"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentDetailsController = void 0;
const common_1 = require("@nestjs/common");
const payment_details_service_1 = require("./payment-details.service");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const swagger_1 = require("@nestjs/swagger");
const payment_details_entity_1 = require("./entities/payment-details.entity");
const payment_details_dto_1 = require("./dto/payment-details.dto");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const roles_guard_1 = require("../auth/guards/roles.guard");
const enum_invoice_status_1 = require("../invoice/enums/enum-invoice-status");
const bulk_payment_details_dto_1 = require("./dto/bulk-payment-details.dto");
const bulk_payment_response_dto_1 = require("./dto/bulk-payment-response.dto");
const tab_activity_service_1 = require("../tab-activity/tab-activity.service");
const tab_activity_enums_1 = require("../tab-activity/enums/tab-activity.enums");
const edit_payment_details_dto_1 = require("./dto/edit-payment-details.dto");
const delete_payment_details_dto_1 = require("./dto/delete-payment-details.dto");
let PaymentDetailsController = class PaymentDetailsController {
    constructor(logger, paymentDetailsService, tabActivitiesService) {
        this.logger = logger;
        this.paymentDetailsService = paymentDetailsService;
        this.tabActivitiesService = tabActivitiesService;
    }
    createPaymentDetails(paymentDetailsDto, req) {
        try {
            this.logger.log('Creating new payment detail', {
                dto: paymentDetailsDto
            });
            return this.paymentDetailsService.createPaymentDetails(paymentDetailsDto, req.user.clinicId, req.user.brandId, req.user.userId);
        }
        catch (error) {
            this.logger.error('Error creating new payment detail', {
                error,
                paymentDetailsDto
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    getPaymentDetailsForPatientsOwner(patientId) {
        try {
            this.logger.log('Fetching payment details by patient ID', {
                patientId
            });
            return this.paymentDetailsService.getPaymentDetailsForPatientsOwner(patientId);
        }
        catch (error) {
            this.logger.error('Error fetching payment details by patient ID', {
                error
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
        }
    }
    async getPaymentDetailsForPatient(patientId, search, page, limit) {
        try {
            // Validate patientId exists
            if (!patientId) {
                throw new common_1.HttpException('Patient ID is required', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log('Fetching payment details for patient', {
                patientId,
                search,
                page,
                limit
            });
            // Parse page and limit to numbers with defaults if provided
            const pageNumber = 1;
            const limitNumber = 10000;
            const result = await this.paymentDetailsService.getPaymentDetailsForPatient(patientId, search, pageNumber, limitNumber);
            // Return result even if empty (don't throw error for empty results)
            return result;
        }
        catch (error) {
            this.logger.error('Error fetching payment details for patient', {
                error,
                patientId,
                search
            });
            // Handle specific error cases
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to fetch payment details for patient', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getInvoicesForPatient(patientId, search, page, limit, invoiceType) {
        try {
            // Validate patientId exists
            if (!patientId) {
                throw new common_1.HttpException('Patient ID is required', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log('Fetching invoices for patient', {
                patientId,
                search,
                page,
                limit,
                invoiceType
            });
            // Parse page and limit to numbers with defaults if provided
            const pageNumber = 1;
            const limitNumber = 10000;
            const result = await this.paymentDetailsService.getInvoicesForPatient(patientId, search, pageNumber, limitNumber, invoiceType);
            // Return result even if empty (don't throw error for empty results)
            return result;
        }
        catch (error) {
            this.logger.error('Error fetching invoices for patient', {
                error,
                patientId,
                search
            });
            // Handle specific error cases
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to fetch invoices for patient', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPaymentDetailsForOwner(ownerId, startDate, endDate, petName, paymentMode, paymentType, userId, searchTerm, page, limit) {
        try {
            // Validate ownerId exists
            if (!ownerId) {
                throw new common_1.HttpException('Owner ID is required', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log('Fetching payment details by owner ID', {
                ownerId,
                filters: {
                    startDate,
                    endDate,
                    petName,
                    paymentMode,
                    paymentType,
                    userId,
                    searchTerm,
                    page,
                    limit
                }
            });
            // Parse page and limit to numbers with defaults if provided
            const pageNumber = page ? parseInt(page, 10) : 1;
            const limitNumber = limit ? parseInt(limit, 10) : 20;
            const filters = {
                startDate,
                endDate,
                petName,
                paymentMode,
                paymentType,
                userId,
                searchTerm,
                page: pageNumber,
                limit: limitNumber
            };
            const result = await this.paymentDetailsService.getPaymentDetailsForOwner(ownerId, filters);
            // Return result even if empty (don't throw error for empty results)
            return result;
        }
        catch (error) {
            this.logger.error('Error fetching payment details by owner ID', {
                error,
                ownerId
            });
            // Handle specific error cases
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to fetch payment details', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    getIndividualPaymentDetail(id) {
        try {
            this.logger.log('Fetching payment details for ID', {
                id
            });
            return this.paymentDetailsService.findOne(id);
        }
        catch (error) {
            this.logger.error('Error fetching payment details for ID', {
                error
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
        }
    }
    deleteLegderFileKeyForPayment(id) {
        try {
            this.logger.log('updating payment details for ID', {
                id
            });
            return this.paymentDetailsService.deleteLedgerFileKey(id);
        }
        catch (error) {
            this.logger.error('Error updating payment details for ID', {
                error
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
        }
    }
    getOwnerInvoicesWithPayments(ownerId, req, page, limit, startDate, endDate, petName, status, paymentMode, searchTerm, userId, invoiceType) {
        try {
            this.logger.log('Fetching owner invoices with payments', {
                ownerId,
                page,
                limit,
                filters: {
                    startDate,
                    endDate,
                    petName,
                    status,
                    paymentMode,
                    searchTerm,
                    userId,
                    invoiceType
                },
                userId: req.user.userId
            });
            const filters = {
                startDate,
                endDate,
                petName,
                status,
                paymentMode,
                searchTerm,
                userId,
                invoiceType
            };
            // Parse pagination parameters
            const pageNumber = page ? parseInt(page, 10) : 1;
            const limitNumber = limit ? parseInt(limit, 10) : 10;
            return this.paymentDetailsService.getOwnerInvoicesWithPayments(ownerId, filters, req.user.userId, req.user.brandId, req.user.clinicId, pageNumber, limitNumber);
        }
        catch (error) {
            this.logger.error('Error fetching owner invoices with payments', {
                error,
                ownerId
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    getOwnerPendingInvoices(ownerId, req, startDate, endDate, petName, searchTerm) {
        try {
            this.logger.log('Fetching pending invoices for owner', {
                ownerId,
                userId: req.user.userId,
                filters: {
                    startDate,
                    endDate,
                    petName,
                    searchTerm
                }
            });
            const filters = {
                startDate,
                endDate,
                petName,
                status: [
                    enum_invoice_status_1.EnumInvoiceStatus.PENDING,
                    enum_invoice_status_1.EnumInvoiceStatus.PARTIALLY_PAID
                ].join(','),
                searchTerm
            };
            return this.paymentDetailsService.getOwnerPendingInvoices(ownerId, req.user.userId, filters, req.user.brandId, req.user.clinicId);
        }
        catch (error) {
            this.logger.error('Error fetching pending invoices for owner', {
                error,
                ownerId
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    getOwnerLedger(ownerId, req) {
        try {
            this.logger.log('Fetching ledger for owner', {
                ownerId,
                userId: req.user.userId
            });
            return this.paymentDetailsService.getOwnerLedger(ownerId, req.user.userId, req.user.brandId, req.user.clinicId);
        }
        catch (error) {
            this.logger.error('Error fetching ledger for owner', {
                error,
                ownerId
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    createBulkPaymentDetails(bulkPaymentDetailsDto, req) {
        try {
            this.logger.log('Creating bulk payment details', {
                dto: bulkPaymentDetailsDto
            });
            return this.paymentDetailsService.createBulkPaymentDetails(bulkPaymentDetailsDto, req.user.clinicId, req.user.brandId, req.user.userId);
        }
        catch (error) {
            this.logger.error('Error creating bulk payment details', {
                error,
                bulkPaymentDetailsDto
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async checkPaymentDocumentStatus(referenceAlphaId, req) {
        try {
            this.logger.log('Checking payment document status', {
                referenceAlphaId,
                userId: req.user.userId
            });
            // Check if required parameter is provided
            if (!referenceAlphaId) {
                throw new common_1.HttpException('Missing required parameter: referenceAlphaId', common_1.HttpStatus.BAD_REQUEST);
            }
            const result = await this.paymentDetailsService.checkPaymentDocumentStatus(referenceAlphaId);
            // If no result found, throw 404 error
            if (!result) {
                throw new common_1.HttpException('Payment document not found', common_1.HttpStatus.NOT_FOUND);
            }
            return result;
        }
        catch (error) {
            this.logger.error('Error checking payment document status', {
                error,
                referenceAlphaId
            });
            // If error is already an HttpException, rethrow it
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException(error.message ||
                'Error checking payment document status', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async handlePaymentDocument(referenceAlphaId, documentType, action, shareMethod, recipient, email, phoneNumber, req) {
        this.logger.log('Handling payment document request', {
            referenceAlphaId,
            documentType,
            action,
            shareMethod,
            recipient,
            email,
            phoneNumber,
            userId: req.user.userId
        });
        try {
            this.logger.log('Handling payment document operation', {
                referenceAlphaId,
                documentType,
                action,
                shareMethod,
                recipient,
                userId: req.user.userId
            });
            // Check if required parameters are provided
            if (!referenceAlphaId || !documentType || !action) {
                throw new common_1.HttpException('Missing required parameters', common_1.HttpStatus.BAD_REQUEST);
            }
            // If action is share, shareMethod is required
            if (action === 'share' && !shareMethod) {
                throw new common_1.HttpException('Share method is required for share action', common_1.HttpStatus.BAD_REQUEST);
            }
            // Validate recipient-related fields
            if (action === 'share' &&
                recipient === 'other' &&
                !email &&
                !phoneNumber) {
                throw new common_1.HttpException('Email or phone number is required when recipient is "other"', common_1.HttpStatus.BAD_REQUEST);
            }
            const result = await this.paymentDetailsService.handlePaymentDocument(referenceAlphaId, documentType, action, shareMethod, req.user.brandId, req.user.userId, recipient, email, phoneNumber);
            // If no result found, throw 404 error
            if (!result) {
                throw new common_1.HttpException('Payment document not found', common_1.HttpStatus.NOT_FOUND);
            }
            // Create tab activity for the document action
            try {
                this.logger.log('Creating tab activity for payment document', {
                    referenceAlphaId,
                    action,
                    userId: req.user.userId
                });
                // Get payment details directly from the service
                const paymentDetails = await this.paymentDetailsService.findPaymentsByReferenceAlphaId(referenceAlphaId, req.user.brandId);
                this.logger.log('Retrieved payment details', {
                    paymentDetailsCount: paymentDetails === null || paymentDetails === void 0 ? void 0 : paymentDetails.length,
                    hasPaymentDetails: !!paymentDetails
                });
                if (paymentDetails && paymentDetails.length > 0) {
                    // Find the first payment with a patient ID
                    this.logger.log('Processing payment activity', {
                        referenceAlphaId,
                        clinicId: req.user.clinicId,
                        brandId: req.user.brandId
                    });
                    const tabActivity = await this.tabActivitiesService.create({
                        clinicId: req.user.clinicId,
                        brandId: req.user.brandId,
                        tabName: tab_activity_enums_1.TabName.INVOICES,
                        actionType: action === 'download'
                            ? tab_activity_enums_1.ActionType.DOWNLOAD
                            : tab_activity_enums_1.ActionType.SHARE,
                        referenceId: referenceAlphaId
                    }, req.user.userId);
                    this.logger.log('Created tab activity', {
                        tabActivityId: tabActivity.id,
                        actionType: tabActivity.actionType,
                        tabName: tabActivity.tabName
                    });
                    // Add tab activity to the result
                    if (result && typeof result === 'object') {
                        result.tabActivity = tabActivity;
                    }
                }
                else {
                    this.logger.warn('No payment details found', {
                        referenceAlphaId
                    });
                }
            }
            catch (error) {
                // Just log the error but don't fail the entire request if tab activity creation fails
                this.logger.error('Error creating tab activity', {
                    error: error.message,
                    stack: error.stack,
                    referenceAlphaId
                });
            }
            return result;
        }
        catch (error) {
            this.logger.error('Error handling payment document', {
                error,
                referenceAlphaId,
                documentType,
                action,
                shareMethod,
                recipient,
                email,
                phoneNumber
            });
            // If error is already an HttpException, rethrow it
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException(error.message || 'Error handling payment document', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async editPaymentDetails(id, editDto, req) {
        try {
            this.logger.log('Editing payment detail', {
                paymentDetailId: id,
                editDto,
                userId: req.user.userId
            });
            const result = await this.paymentDetailsService.editPaymentDetails(id, editDto, req.user.userId);
            return result;
        }
        catch (error) {
            this.logger.error('Error editing payment detail', {
                error,
                paymentDetailId: id,
                editDto,
                userId: req.user.userId
            });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException(error.message || 'Failed to edit payment detail', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async deletePaymentDetails(id, deleteDto, req) {
        try {
            this.logger.log('Deleting payment detail', {
                paymentDetailId: id,
                deleteDto,
                userId: req.user.userId
            });
            const result = await this.paymentDetailsService.deletePaymentDetails(id, deleteDto, req.user.userId);
            return result;
        }
        catch (error) {
            this.logger.error('Error deleting payment detail', {
                error,
                paymentDetailId: id,
                deleteDto,
                userId: req.user.userId
            });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException(error.message || 'Failed to delete payment detail', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.PaymentDetailsController = PaymentDetailsController;
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Creates a payment detail entry ',
        type: payment_details_entity_1.PaymentDetailsEntity
    }),
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('createPaymentDetails-payment-details'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [payment_details_dto_1.PaymentDetailsDto, Object]),
    __metadata("design:returntype", void 0)
], PaymentDetailsController.prototype, "createPaymentDetails", null);
__decorate([
    (0, common_1.Get)(':patientId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getPaymentDetailsForPatientsOwner-payment-details'),
    __param(0, (0, common_1.Param)('patientId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentDetailsController.prototype, "getPaymentDetailsForPatientsOwner", null);
__decorate([
    (0, common_1.Get)('patient-receipts/:patientId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getPaymentDetailsForPatient-payment-details'),
    __param(0, (0, common_1.Param)('patientId')),
    __param(1, (0, common_1.Query)('search')),
    __param(2, (0, common_1.Query)('page')),
    __param(3, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", Promise)
], PaymentDetailsController.prototype, "getPaymentDetailsForPatient", null);
__decorate([
    (0, common_1.Get)('patient-invoices/:patientId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getInvoicesForPatient-payment-details'),
    __param(0, (0, common_1.Param)('patientId')),
    __param(1, (0, common_1.Query)('search')),
    __param(2, (0, common_1.Query)('page')),
    __param(3, (0, common_1.Query)('limit')),
    __param(4, (0, common_1.Query)('invoiceType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], PaymentDetailsController.prototype, "getInvoicesForPatient", null);
__decorate([
    (0, common_1.Get)('owner-receipts/:ownerId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getPaymentDetailsForOwner-payment-details'),
    __param(0, (0, common_1.Param)('ownerId')),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __param(3, (0, common_1.Query)('petName')),
    __param(4, (0, common_1.Query)('paymentMode')),
    __param(5, (0, common_1.Query)('paymentType')),
    __param(6, (0, common_1.Query)('userId')),
    __param(7, (0, common_1.Query)('searchTerm')),
    __param(8, (0, common_1.Query)('page')),
    __param(9, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String, String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], PaymentDetailsController.prototype, "getPaymentDetailsForOwner", null);
__decorate([
    (0, common_1.Get)('findOne/:id'),
    (0, track_method_decorator_1.TrackMethod)('getIndividualPaymentDetail'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentDetailsController.prototype, "getIndividualPaymentDetail", null);
__decorate([
    (0, common_1.Put)('delete-ledger/:id'),
    (0, track_method_decorator_1.TrackMethod)('delete-laser-fileKey'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], PaymentDetailsController.prototype, "deleteLegderFileKeyForPayment", null);
__decorate([
    (0, common_1.Get)('owner-invoices/:ownerId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getOwnerInvoicesWithPayments-payment-details'),
    __param(0, (0, common_1.Param)('ownerId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)('page')),
    __param(3, (0, common_1.Query)('limit')),
    __param(4, (0, common_1.Query)('startDate')),
    __param(5, (0, common_1.Query)('endDate')),
    __param(6, (0, common_1.Query)('petName')),
    __param(7, (0, common_1.Query)('status')),
    __param(8, (0, common_1.Query)('paymentMode')),
    __param(9, (0, common_1.Query)('searchTerm')),
    __param(10, (0, common_1.Query)('userId')),
    __param(11, (0, common_1.Query)('invoiceType')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String, String, String, String, String, String, String, String, String, String]),
    __metadata("design:returntype", void 0)
], PaymentDetailsController.prototype, "getOwnerInvoicesWithPayments", null);
__decorate([
    (0, common_1.Get)('pending-invoices/:ownerId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getOwnerPendingInvoices-payment-details'),
    __param(0, (0, common_1.Param)('ownerId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)('startDate')),
    __param(3, (0, common_1.Query)('endDate')),
    __param(4, (0, common_1.Query)('petName')),
    __param(5, (0, common_1.Query)('searchTerm')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String, String, String, String]),
    __metadata("design:returntype", void 0)
], PaymentDetailsController.prototype, "getOwnerPendingInvoices", null);
__decorate([
    (0, common_1.Get)('owner-ledger/:ownerId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getOwnerLedger-payment-details'),
    __param(0, (0, common_1.Param)('ownerId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentDetailsController.prototype, "getOwnerLedger", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Creates multiple payment detail entries for bulk invoice settlement. If no invoiceIds are provided, all pending invoices for the owner will be processed.',
        type: bulk_payment_response_dto_1.BulkPaymentResponse
    }),
    (0, common_1.Post)('bulk'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('createBulkPaymentDetails-payment-details'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [bulk_payment_details_dto_1.BulkPaymentDetailsDto, Object]),
    __metadata("design:returntype", void 0)
], PaymentDetailsController.prototype, "createBulkPaymentDetails", null);
__decorate([
    (0, common_1.Get)('document-status/:referenceAlphaId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Check the status of a payment document generation request',
        description: 'Polls for completion of a payment receipt document'
    }),
    (0, swagger_1.ApiParam)({
        name: 'referenceAlphaId',
        required: true,
        description: 'Reference Alpha ID of the payment to check'
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('checkPaymentDocumentStatus-payment-details'),
    __param(0, (0, common_1.Param)('referenceAlphaId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], PaymentDetailsController.prototype, "checkPaymentDocumentStatus", null);
__decorate([
    (0, common_1.Get)('documents/:referenceAlphaId'),
    (0, swagger_1.ApiParam)({ name: 'referenceAlphaId', type: 'string' }),
    (0, swagger_1.ApiQuery)({
        name: 'documentType',
        enum: ['creditnote', 'payment-details'],
        description: 'Type of payment document'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'action',
        enum: ['share', 'download'],
        description: 'Whether to share or download the document'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'shareMethod',
        enum: ['email', 'whatsapp', 'both'],
        required: false,
        description: 'Method to use for sharing'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'recipient',
        enum: ['client', 'other'],
        required: false,
        description: 'Recipient of the document (client or custom recipient)'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'email',
        type: 'string',
        required: false,
        description: 'Custom email when recipient is "other"'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'phoneNumber',
        type: 'string',
        required: false,
        description: 'Custom phone number when recipient is "other"'
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('handlePaymentDocument-payment-details'),
    __param(0, (0, common_1.Param)('referenceAlphaId')),
    __param(1, (0, common_1.Query)('documentType')),
    __param(2, (0, common_1.Query)('action')),
    __param(3, (0, common_1.Query)('shareMethod')),
    __param(4, (0, common_1.Query)('recipient')),
    __param(5, (0, common_1.Query)('email')),
    __param(6, (0, common_1.Query)('phoneNumber')),
    __param(7, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, Object, Object, Object, Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentDetailsController.prototype, "handlePaymentDocument", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Edits a payment detail (only payment mode)',
        type: payment_details_entity_1.PaymentDetailsEntity
    }),
    (0, swagger_1.ApiOperation)({
        summary: 'Edit payment details',
        description: 'Edit payment mode with mandatory comment. Only authorized staff can edit payment details.'
    }),
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('editPaymentDetails-payment-details'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, edit_payment_details_dto_1.EditPaymentDetailsDto, Object]),
    __metadata("design:returntype", Promise)
], PaymentDetailsController.prototype, "editPaymentDetails", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Deletes a payment detail with business validations'
    }),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete payment details',
        description: 'Delete payment details with mandatory comment. Only authorized staff can delete payment details within 12 hours of creation. Refund receipts cannot be deleted.'
    }),
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('deletePaymentDetails-payment-details'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, delete_payment_details_dto_1.DeletePaymentDetailsDto, Object]),
    __metadata("design:returntype", Promise)
], PaymentDetailsController.prototype, "deletePaymentDetails", null);
exports.PaymentDetailsController = PaymentDetailsController = __decorate([
    (0, common_1.Controller)('payment-details'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        payment_details_service_1.PaymentDetailsService,
        tab_activity_service_1.TabActivitiesService])
], PaymentDetailsController);
//# sourceMappingURL=payment-details.controller.js.map